import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ChainLogo } from "@/components/TokenLogo";
import { useWallet } from "@/contexts/WalletContext";
import { getChainConfig } from "@/lib/blockchain-config";
import { formatAddress } from "@/lib/utils";
import { Wallet, ExternalLink, Copy, LogOut } from "lucide-react";
import { toast } from "sonner";

interface WalletConnectionProps {
  chainId: string;
  className?: string;
}

export function WalletConnection({ chainId, className }: WalletConnectionProps) {
  const { getWalletForChain, isChainConnected, connectSolanaWallet, connectEvmWallet, disconnectSolanaWallet, disconnectEvmWallet } = useWallet();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const chainConfig = getChainConfig(chainId);
  const wallet = getWalletForChain(chainId);
  const isConnected = isChainConnected(chainId);

  if (!chainConfig) {
    return null;
  }

  const handleConnect = async () => {
    try {
      if (chainConfig.type === 'solana') {
        await connectSolanaWallet();
      } else {
        await connectEvmWallet(chainId);
      }
      setIsDialogOpen(false);
      toast.success(`Connected to ${chainConfig.name}`);
    } catch (error) {
      toast.error(`Failed to connect to ${chainConfig.name}`);
    }
  };

  const handleDisconnect = () => {
    if (chainConfig.type === 'solana') {
      disconnectSolanaWallet();
    } else {
      disconnectEvmWallet();
    }
    toast.success(`Disconnected from ${chainConfig.name}`);
  };

  const copyAddress = () => {
    if (wallet.address) {
      navigator.clipboard.writeText(wallet.address);
      toast.success('Address copied to clipboard');
    }
  };

  const openExplorer = () => {
    if (wallet.address && chainConfig.explorerUrl) {
      const url = `${chainConfig.explorerUrl}/address/${wallet.address}`;
      window.open(url, '_blank');
    }
  };

  if (isConnected && wallet.address) {
    return (
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className={`border-primary/20 hover:border-primary/40 ${className}`}>
            <div className="flex items-center gap-2">
              <ChainLogo
                src={chainConfig.logo}
                alt={chainConfig.name}
                className="w-4 h-4 rounded-full"
              />
              <span>{formatAddress(wallet.address)}</span>
              <Badge variant="outline" className="border-success text-success text-xs">
                Connected
              </Badge>
            </div>
          </Button>
        </DialogTrigger>
        
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ChainLogo
                src={chainConfig.logo}
                alt={chainConfig.name}
                className="w-5 h-5 rounded-full"
              />
              {chainConfig.name} Wallet
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="p-4 bg-secondary/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-muted-foreground">Address</span>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" onClick={copyAddress}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={openExplorer}>
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="font-mono text-sm break-all">{wallet.address}</p>
            </div>

            {wallet.balance && (
              <div className="p-4 bg-secondary/50 rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">Balance</div>
                <p className="text-lg font-semibold">
                  {wallet.balance} {chainConfig.nativeCurrency.symbol}
                </p>
              </div>
            )}

            <Button 
              variant="outline" 
              className="w-full border-destructive text-destructive hover:bg-destructive hover:text-destructive-foreground"
              onClick={handleDisconnect}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Disconnect
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={`border-primary/20 hover:border-primary/40 ${className}`}>
          <Wallet className="h-4 w-4 mr-2" />
          Connect {chainConfig.name}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ChainLogo
              src={chainConfig.logo}
              alt={chainConfig.name}
              className="w-5 h-5 rounded-full"
            />
            Connect to {chainConfig.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {wallet.error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">{wallet.error}</p>
            </div>
          )}

          <WalletOptions 
            chainConfig={chainConfig} 
            onConnect={handleConnect}
            connecting={wallet.connecting}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface WalletOptionsProps {
  chainConfig: any;
  onConnect: () => void;
  connecting: boolean;
}

function WalletOptions({ chainConfig, onConnect, connecting }: WalletOptionsProps) {
  if (chainConfig.type === 'solana') {
    return (
      <div className="space-y-3">
        <Card className="p-4 hover:bg-secondary/50 cursor-pointer transition-colors" onClick={onConnect}>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">P</span>
            </div>
            <div>
              <h3 className="font-semibold">Phantom</h3>
              <p className="text-sm text-muted-foreground">Connect using Phantom wallet</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4 hover:bg-secondary/50 cursor-pointer transition-colors opacity-50">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">S</span>
            </div>
            <div>
              <h3 className="font-semibold">Solflare</h3>
              <p className="text-sm text-muted-foreground">Coming soon</p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Card className="p-4 hover:bg-secondary/50 cursor-pointer transition-colors" onClick={onConnect}>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">M</span>
          </div>
          <div>
            <h3 className="font-semibold">MetaMask</h3>
            <p className="text-sm text-muted-foreground">Connect using MetaMask wallet</p>
          </div>
        </div>
      </Card>
      
      <Card className="p-4 hover:bg-secondary/50 cursor-pointer transition-colors opacity-50">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">W</span>
          </div>
          <div>
            <h3 className="font-semibold">WalletConnect</h3>
            <p className="text-sm text-muted-foreground">Coming soon</p>
          </div>
        </div>
      </Card>
    </div>
  );
}

// Quick connect button for specific chains
export function QuickConnectButton({ chainId }: { chainId: string }) {
  const { isChainConnected, getWalletForChain } = useWallet();
  const chainConfig = getChainConfig(chainId);
  const wallet = getWalletForChain(chainId);
  
  if (!chainConfig) return null;
  
  if (isChainConnected(chainId)) {
    return (
      <Badge variant="outline" className="border-success text-success">
        <ChainLogo
          src={chainConfig.logo}
          alt={chainConfig.name}
          className="w-3 h-3 rounded-full mr-1"
        />
        Connected
      </Badge>
    );
  }
  
  return (
    <WalletConnection 
      chainId={chainId} 
      className="h-8 px-3 text-xs"
    />
  );
}
