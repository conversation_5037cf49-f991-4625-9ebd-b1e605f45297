import { useState, useCallback } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { useTokenPrices } from '@/lib/price-service';
import { getTokenConfig, getNativeTokensForChain } from '@/lib/blockchain-config';
import { useBridge } from '@/hooks/useBridge';
import { Connection, PublicKey, Transaction } from '@solana/web3.js';

interface SwapState {
  fromToken: string;
  toToken: string;
  amount: string;
  recipientAddress: string;
  isLoading: boolean;
  error: string | null;
}

interface SwapQuote {
  inAmount: string;
  outAmount: string;
  rate: string;
  priceImpact: string;
  fee: string;
  route: any;
}

export function useSwap() {
  const { solanaWallet } = useWallet();
  const { prices, loading: pricesLoading } = useTokenPrices(['SOL', 'USDC', 'BONK', 'WIF']);
  const { getTokenBalance: getBridgeTokenBalance } = useBridge(); // Use real balances from bridge

  const [swapState, setSwapState] = useState<SwapState>({
    fromToken: 'SOL',
    toToken: 'USDC',
    amount: '',
    recipientAddress: '',
    isLoading: false,
    error: null,
  });

  const [swapQuote, setSwapQuote] = useState<SwapQuote | null>(null);
  const [isLoadingQuote, setIsLoadingQuote] = useState(false);

  const updateSwapState = useCallback((updates: Partial<SwapState>) => {
    setSwapState(prev => ({ ...prev, ...updates }));
  }, []);

  const getSupportedTokens = useCallback(() => {
    return getNativeTokensForChain('solana'); // Only show Solana native tokens
  }, []);

  const getTokenBalance = useCallback((chainId: string, tokenSymbol: string): string => {
    return getBridgeTokenBalance(chainId, tokenSymbol); // Use real balances
  }, [getBridgeTokenBalance]);

  const getTokenPrice = useCallback((tokenSymbol: string): number => {
    return prices[tokenSymbol] || 0;
  }, [prices]);

  const getUsdValue = useCallback((amount: string, tokenSymbol: string): number => {
    const price = getTokenPrice(tokenSymbol);
    return parseFloat(amount || '0') * price;
  }, [getTokenPrice]);

  const formatUsdValue = useCallback((value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }, []);

  const validateSwap = useCallback(() => {
    if (!swapState.amount || parseFloat(swapState.amount) <= 0) {
      return { isValid: false, error: 'Enter swap amount' };
    }

    if (swapState.fromToken === swapState.toToken) {
      return { isValid: false, error: 'Select different tokens' };
    }

    const balance = parseFloat(getTokenBalance('solana', swapState.fromToken));
    const amount = parseFloat(swapState.amount);

    if (amount > balance) {
      return { isValid: false, error: `Insufficient ${swapState.fromToken} balance` };
    }

    if (!swapState.recipientAddress) {
      return { isValid: false, error: 'Enter recipient address' };
    }

    try {
      new PublicKey(swapState.recipientAddress);
    } catch {
      return { isValid: false, error: 'Invalid Solana address' };
    }

    if (!solanaWallet.isConnected) {
      return { isValid: false, error: 'Connect wallet' };
    }

    return { isValid: true };
  }, [swapState, getTokenBalance, solanaWallet.isConnected]);

  const getSwapQuote = useCallback(async () => {
    if (!swapState.amount || parseFloat(swapState.amount) <= 0) {
      setSwapQuote(null);
      return;
    }

    setIsLoadingQuote(true);
    
    try {
      // Simulate Jupiter API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const fromTokenConfig = getTokenConfig(swapState.fromToken);
      const toTokenConfig = getTokenConfig(swapState.toToken);
      
      if (!fromTokenConfig || !toTokenConfig) {
        throw new Error('Token configuration not found');
      }

      // Mock quote calculation
      const inAmount = swapState.amount;
      const fromPrice = getTokenPrice(swapState.fromToken);
      const toPrice = getTokenPrice(swapState.toToken);
      
      // Simple rate calculation (in real app, Jupiter would provide this)
      const rate = fromPrice / toPrice;
      const outAmount = (parseFloat(inAmount) * rate * 0.997).toFixed(6); // 0.3% slippage
      const priceImpact = '0.1'; // Mock price impact
      const fee = '0.00025'; // Mock SOL fee

      const quote: SwapQuote = {
        inAmount,
        outAmount,
        rate: rate.toFixed(6),
        priceImpact,
        fee,
        route: null, // Jupiter route data would go here
      };

      setSwapQuote(quote);
    } catch (error) {
      console.error('Failed to get swap quote:', error);
      setSwapQuote(null);
    } finally {
      setIsLoadingQuote(false);
    }
  }, [swapState.amount, swapState.fromToken, swapState.toToken, getTokenPrice]);

  const executeSwap = useCallback(async () => {
    const validation = validateSwap();
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    if (!swapQuote) {
      throw new Error('No swap quote available');
    }

    setSwapState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🔄 Executing swap:', {
        from: `${swapState.amount} ${swapState.fromToken}`,
        to: `${swapQuote.outAmount} ${swapState.toToken}`,
        recipient: swapState.recipientAddress,
      });

      // In a real implementation, this would:
      // 1. Get Jupiter swap transaction
      // 2. Add transfer instruction to recipient
      // 3. Sign and send transaction

      // Mock Jupiter API integration
      const connection = new Connection('https://api.devnet.solana.com');
      
      // Simulate transaction creation and signing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock transaction result
      const txSignature = `swap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      console.log('✅ Swap completed:', txSignature);
      console.log(`📤 Sent ${swapQuote.outAmount} ${swapState.toToken} to ${swapState.recipientAddress}`);

      // Reset form
      setSwapState(prev => ({
        ...prev,
        amount: '',
        recipientAddress: '',
        isLoading: false,
      }));

      setSwapQuote(null);

      return {
        signature: txSignature,
        fromAmount: swapState.amount,
        fromToken: swapState.fromToken,
        toAmount: swapQuote.outAmount,
        toToken: swapState.toToken,
        recipient: swapState.recipientAddress,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Swap failed';
      setSwapState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false,
      }));
      throw error;
    }
  }, [swapState, swapQuote, validateSwap, solanaWallet]);

  return {
    // State
    swapState,
    swapQuote,
    isLoadingQuote,
    pricesLoading,
    
    // Actions
    updateSwapState,
    executeSwap,
    getSwapQuote,
    
    // Utilities
    validateSwap,
    getSupportedTokens,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
  };
}
