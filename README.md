# SolBridge - Cross-Chain Bridge

A security-first cross-chain bridge connecting Solana to EVM chains (Base, Arbitrum) using Wormhole infrastructure.

## 🌟 Features

### ✅ Implemented
- **Multi-Chain Support**: Solana ↔ Base/Arbitrum bridging
- **Wallet Integration**: Phantom (Solana) and MetaMask (EVM) support
- **Real-time Monitoring**: Transaction status tracking and notifications
- **Security Assessment**: Risk evaluation for each bridge operation
- **Token Support**: USDC, USDT, SOL, ETH cross-chain transfers
- **Fee Calculation**: Dynamic fee estimation with transparent breakdown
- **Network Status**: Real-time network health monitoring
- **Transaction History**: Complete bridge transaction tracking

### 🔧 Architecture
- **Frontend**: React + TypeScript + Tailwind CSS + shadcn/ui
- **Bridge Protocol**: Wormhole SDK integration
- **Wallet Adapters**: Solana Wallet Adapter + Wagmi (EVM)
- **State Management**: React Context + Custom Hooks
- **Notifications**: Sonner toast system

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Phantom wallet (for Solana)
- MetaMask (for EVM chains)

### Installation

```bash
# Clone the repository
git clone <YOUR_GIT_URL>
cd solara-nexus

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup
The app automatically detects the environment:
- **Development**: Uses testnets (Solana Devnet, Base Sepolia, Arbitrum Sepolia)
- **Production**: Uses mainnets

## 🏗️ Project Structure

```
src/
├── components/          # React components
│   ├── BridgeInterface.tsx    # Main bridge UI
│   ├── WalletConnection.tsx   # Wallet connection logic
│   ├── TransactionHistory.tsx # Transaction tracking
│   └── NetworkStatus.tsx      # Network monitoring
├── contexts/           # React contexts
│   └── WalletContext.tsx     # Wallet state management
├── hooks/              # Custom React hooks
│   ├── useBridge.ts          # Bridge operations
│   └── useTransactionMonitor.ts # Transaction monitoring
├── lib/                # Utilities and services
│   ├── blockchain-config.ts   # Chain configurations
│   ├── wormhole-service.ts   # Wormhole integration
│   └── utils.ts              # Helper functions
└── pages/              # Page components
    └── Index.tsx             # Main landing page
```

## 🔗 Supported Networks

| Network | Mainnet | Testnet | Tokens |
|---------|---------|---------|---------|
| Solana | ✅ | ✅ | SOL, USDC, USDT |
| Base | ✅ | ✅ | ETH, USDC, USDT |
| Arbitrum | ✅ | ✅ | ETH, USDC, USDT |

## 🛡️ Security Features

- **Risk Assessment**: Automated security scoring for each transaction
- **Fee Transparency**: Clear breakdown of bridge and network fees
- **Transaction Monitoring**: Real-time status updates and confirmations
- **Network Health**: Continuous monitoring of all supported networks
- **Error Handling**: Comprehensive error reporting and retry mechanisms

## 🔧 Technical Implementation

### Wormhole Integration
- Uses Wormhole TypeScript SDK for cross-chain messaging
- Implements Token Bridge protocol for asset transfers
- Supports both native and wrapped token transfers

### Wallet Management
- **Solana**: Phantom wallet integration with auto-connect
- **EVM**: MetaMask integration with chain switching
- **Multi-chain**: Simultaneous connections to multiple networks

### Transaction Flow
1. User selects source/destination chains and token
2. Wallet connection verification
3. Fee calculation and risk assessment
4. Transaction initiation on source chain
5. VAA (Verifiable Action Approval) generation
6. Redemption on destination chain
7. Real-time status monitoring

## 🚧 Development Status

This is a **prototype implementation** using Wormhole SDK. For production use:

1. **Smart Contract Audits**: Implement comprehensive security audits
2. **Liquidity Management**: Add liquidity pool monitoring
3. **Advanced Features**: Implement Token-2022 support, batch transfers
4. **Performance**: Optimize for high-volume transactions
5. **Monitoring**: Add comprehensive analytics and alerting

## 📚 Resources

- [Wormhole Documentation](https://wormhole.com/docs/)
- [Solana Documentation](https://docs.solana.com/)
- [Base Documentation](https://docs.base.org/)
- [Arbitrum Documentation](https://docs.arbitrum.io/)

## 🤝 Contributing

This project leverages existing cross-chain infrastructure rather than building custom smart contracts, making it faster to implement and more secure by using battle-tested protocols.

## 📄 License

This project is built for educational and development purposes. Please ensure proper security audits before any production deployment.
