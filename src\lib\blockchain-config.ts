import { Network } from '@wormhole-foundation/sdk';

// Chain configurations for our supported networks
export interface ChainConfig {
  id: string;
  name: string;
  logo: string;
  wormholeChainId: number;
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  type: 'solana' | 'evm';
}

// Token configurations
export interface TokenConfig {
  symbol: string;
  name: string;
  decimals: number;
  logoUrl?: string;
  addresses: {
    [chainId: string]: string;
  };
  isNative?: boolean;
}

// Supported chains configuration
export const SUPPORTED_CHAINS: Record<string, ChainConfig> = {
  solana: {
    id: 'solana',
    name: '<PERSON><PERSON>',
    logo: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
    wormholeChainId: 1,
    rpcUrl: process.env.NODE_ENV === 'production'
      ? 'https://api.mainnet-beta.solana.com'
      : 'https://api.devnet.solana.com',
    explorerUrl: 'https://explorer.solana.com',
    nativeCurrency: {
      name: '<PERSON><PERSON>',
      symbol: 'SOL',
      decimals: 9,
    },
    type: 'solana',
  },
  base: {
    id: 'base',
    name: 'Base',
    logo: 'https://avatars.githubusercontent.com/u/108554348?s=280&v=4',
    wormholeChainId: 30,
    rpcUrl: process.env.NODE_ENV === 'production'
      ? 'https://mainnet.base.org'
      : 'https://sepolia.base.org',
    explorerUrl: process.env.NODE_ENV === 'production'
      ? 'https://basescan.org'
      : 'https://sepolia.basescan.org',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    type: 'evm',
  },
  arbitrum: {
    id: 'arbitrum',
    name: 'Arbitrum',
    logo: 'https://assets.coingecko.com/coins/images/16547/large/photo_2023-03-29_21.47.00.jpeg',
    wormholeChainId: 23,
    rpcUrl: process.env.NODE_ENV === 'production'
      ? 'https://arb1.arbitrum.io/rpc'
      : 'https://sepolia-rollup.arbitrum.io/rpc',
    explorerUrl: process.env.NODE_ENV === 'production'
      ? 'https://arbiscan.io'
      : 'https://sepolia.arbiscan.io',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    type: 'evm',
  },
  ethereum: {
    id: 'ethereum',
    name: 'Ethereum',
    logo: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
    wormholeChainId: 2,
    rpcUrl: process.env.NODE_ENV === 'production'
      ? 'https://eth.llamarpc.com'
      : 'https://ethereum-sepolia.publicnode.com',
    explorerUrl: process.env.NODE_ENV === 'production'
      ? 'https://etherscan.io'
      : 'https://sepolia.etherscan.io',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    type: 'evm',
  },
  polygon: {
    id: 'polygon',
    name: 'Polygon',
    logo: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
    wormholeChainId: 5,
    rpcUrl: process.env.NODE_ENV === 'production'
      ? 'https://polygon.llamarpc.com'
      : 'https://rpc-amoy.polygon.technology',
    explorerUrl: process.env.NODE_ENV === 'production'
      ? 'https://polygonscan.com'
      : 'https://amoy.polygonscan.com',
    nativeCurrency: {
      name: 'Polygon',
      symbol: 'MATIC',
      decimals: 18,
    },
    type: 'evm',
  },
};

// Supported tokens configuration
export const SUPPORTED_TOKENS: Record<string, TokenConfig> = {
  // Native tokens
  SOL: {
    symbol: 'SOL',
    name: 'Solana',
    decimals: 9,
    logoUrl: 'https://assets.coingecko.com/coins/images/4128/large/solana.png',
    isNative: true,
    addresses: {
      solana: 'So11111111111111111111111111111111111111112', // Wrapped SOL
    },
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
    isNative: true,
    addresses: {
      ethereum: '******************************************', // Native ETH
      base: '******************************************', // Native ETH
      arbitrum: '******************************************', // Native ETH
      solana: '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs', // Wrapped ETH on Solana
    },
  },
  MATIC: {
    symbol: 'MATIC',
    name: 'Polygon',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
    isNative: true,
    addresses: {
      polygon: '******************************************', // Native MATIC
      ethereum: '******************************************', // MATIC on Ethereum
      solana: 'C7NNPWuZCNjZBfW5p6JvGsR8pUdsRpEdP1ZAhnoDwj7h', // MATIC on Solana
    },
  },

  // Stablecoins
  USDC: {
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/6319/large/USD_Coin_icon.png',
    addresses: {
      // Devnet USDC address
      solana: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU',
      ethereum: '******************************************',
      base: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/325/large/Tether.png',
    addresses: {
      solana: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      ethereum: '******************************************',
      base: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },
  DAI: {
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/9956/large/Badge_Dai.png',
    addresses: {
      solana: 'EjmyN6qEC1Tf1JxiG1ae7UTJhUxSwk1TCWNWqxWV4J6o',
      ethereum: '******************************************',
      polygon: '******************************************',
    },
  },

  // Major DeFi tokens
  WBTC: {
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    decimals: 8,
    logoUrl: 'https://assets.coingecko.com/coins/images/7598/large/wrapped_bitcoin_wbtc.png',
    addresses: {
      solana: '********************************************',
      ethereum: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },
  UNI: {
    symbol: 'UNI',
    name: 'Uniswap',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
    addresses: {
      solana: '8FU95xFJhUUkyyCLU13HSzDLs7oC4QZdXQHL6SCeab36',
      ethereum: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },
  LINK: {
    symbol: 'LINK',
    name: 'Chainlink',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
    addresses: {
      solana: '2wpTofQ8SkACrkZWrZDjXPitYa8AwWgX8AfxdeBRRVLX',
      ethereum: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },
  AAVE: {
    symbol: 'AAVE',
    name: 'Aave',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/aave-aave-logo.png',
    addresses: {
      solana: '********************************************',
      ethereum: '******************************************',
      arbitrum: '******************************************',
      polygon: '******************************************',
    },
  },

  // Popular meme/gaming tokens
  SHIB: {
    symbol: 'SHIB',
    name: 'Shiba Inu',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/shiba-inu-shib-logo.png',
    addresses: {
      solana: 'CiKu4eHsVrc1eueVQeHn7qhXTcVu95gSQmBpX4utjL9z',
      ethereum: '******************************************',
    },
  },
  PEPE: {
    symbol: 'PEPE',
    name: 'Pepe',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/29850/large/pepe-token.jpeg',
    addresses: {
      ethereum: '******************************************',
      base: '******************************************',
    },
  },
  DOGE: {
    symbol: 'DOGE',
    name: 'Dogecoin',
    decimals: 8,
    logoUrl: 'https://cryptologos.cc/logos/dogecoin-doge-logo.png',
    addresses: {
      ethereum: '******************************************',
    },
  },

  // More Ethereum DeFi tokens
  CRV: {
    symbol: 'CRV',
    name: 'Curve DAO Token',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/12124/large/Curve.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
    },
  },
  COMP: {
    symbol: 'COMP',
    name: 'Compound',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/10775/large/COMP.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
    },
  },
  MKR: {
    symbol: 'MKR',
    name: 'Maker',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/1364/large/Mark_Maker.png',
    addresses: {
      ethereum: '******************************************',
    },
  },
  SNX: {
    symbol: 'SNX',
    name: 'Synthetix',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/3406/large/SNX.png',
    addresses: {
      ethereum: '******************************************',
    },
  },
  SUSHI: {
    symbol: 'SUSHI',
    name: 'SushiSwap',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/12271/large/512x512_Logo_no_chop.png',
    addresses: {
      ethereum: '******************************************',
      polygon: '******************************************',
    },
  },

  // Layer 2 tokens
  ARB: {
    symbol: 'ARB',
    name: 'Arbitrum',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/arbitrum-arb-logo.png',
    addresses: {
      arbitrum: '******************************************',
      ethereum: '******************************************',
    },
  },
  OP: {
    symbol: 'OP',
    name: 'Optimism',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/optimism-ethereum-op-logo.png',
    addresses: {
      ethereum: '******************************************',
    },
  },

  // Solana ecosystem tokens
  RAY: {
    symbol: 'RAY',
    name: 'Raydium',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/13928/large/PSigc4ie_400x400.jpg',
    addresses: {
      solana: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
    },
  },
  SRM: {
    symbol: 'SRM',
    name: 'Serum',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/11970/large/serum-logo.png',
    addresses: {
      solana: 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
    },
  },
  ORCA: {
    symbol: 'ORCA',
    name: 'Orca',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/17547/large/Orca_Logo.png',
    addresses: {
      solana: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
    },
  },
  MNGO: {
    symbol: 'MNGO',
    name: 'Mango',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/17119/large/mango.png',
    addresses: {
      solana: 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac',
    },
  },
  STEP: {
    symbol: 'STEP',
    name: 'Step Finance',
    decimals: 9,
    logoUrl: 'https://assets.coingecko.com/coins/images/16170/large/step-logo.png',
    addresses: {
      solana: 'StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT',
    },
  },
  COPE: {
    symbol: 'COPE',
    name: 'Cope',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/14570/large/cope.png',
    addresses: {
      solana: '8HGyAAB1yoM1ttS7pXjHMa3dukTFGQggnFFH3hJZgzQh',
    },
  },
  FIDA: {
    symbol: 'FIDA',
    name: 'Bonfida',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/13395/large/bonfida.png',
    addresses: {
      solana: 'EchesyfXePKdLtoiZSL8pBe8Myagyy8ZRqsACNCFGnvp',
    },
  },
  SAMO: {
    symbol: 'SAMO',
    name: 'Samoyedcoin',
    decimals: 9,
    logoUrl: 'https://assets.coingecko.com/coins/images/17547/large/samo.png',
    addresses: {
      solana: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
    },
  },
  BONK: {
    symbol: 'BONK',
    name: 'Bonk',
    decimals: 5,
    logoUrl: 'https://assets.coingecko.com/coins/images/28600/large/bonk.jpg',
    addresses: {
      solana: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    },
  },
  WIF: {
    symbol: 'WIF',
    name: 'dogwifhat',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/33767/large/dogwifhat.jpg',
    addresses: {
      solana: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
    },
  },
  JUP: {
    symbol: 'JUP',
    name: 'Jupiter',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/34188/large/jup.png',
    addresses: {
      solana: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
    },
  },
  PYTH: {
    symbol: 'PYTH',
    name: 'Pyth Network',
    decimals: 6,
    logoUrl: 'https://assets.coingecko.com/coins/images/31916/large/pyth.png',
    addresses: {
      solana: 'HZ1JovNiVvGrGNiiYvEozEVgZ58xaU3RKwX8eACQBCt3',
    },
  },
  RENDER: {
    symbol: 'RENDER',
    name: 'Render Token',
    decimals: 8,
    logoUrl: 'https://assets.coingecko.com/coins/images/11636/large/rndr.png',
    addresses: {
      solana: 'rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof',
      ethereum: '******************************************',
    },
  },

  // Cross-chain tokens
  AVAX: {
    symbol: 'AVAX',
    name: 'Avalanche',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
    addresses: {
      solana: 'KgV1GvrHQmRBY8sHQQeUKwTm2r2h8t4C8qt12Cw1HVE',
      ethereum: '******************************************',
    },
  },
  BNB: {
    symbol: 'BNB',
    name: 'BNB',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
    addresses: {
      solana: '9gP2kCy3wA1ctvYWQk75guqXuHfrEomqydHLtcTCqiLa',
      ethereum: '******************************************',
    },
  },

  // Staking tokens
  stETH: {
    symbol: 'stETH',
    name: 'Lido Staked Ether',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/13442/large/steth_logo.png',
    addresses: {
      solana: 'H2mf9QNdU2Niq6QR7367Ua2trBsvscLyX5bz7R3Pw5sE',
      ethereum: '******************************************',
    },
  },
  LDO: {
    symbol: 'LDO',
    name: 'Lido DAO',
    decimals: 18,
    logoUrl: 'https://assets.coingecko.com/coins/images/13573/large/Lido_DAO.png',
    addresses: {
      solana: 'HZRCwxP2Vq9PCpPXooayhJ2bxTpo5xfpQrwB1svh332p',
      ethereum: '******************************************',
    },
  },
};

// Wormhole network configuration
export const WORMHOLE_NETWORK: Network = process.env.NODE_ENV === 'production' ? 'Mainnet' : 'Testnet';

// Bridge fee configuration
export const BRIDGE_CONFIG = {
  baseFeePercentage: 0.001, // 0.1%
  minimumFeeUSD: 1.0,
  maximumFeeUSD: 100.0,
  estimatedTimeMinutes: 3,
};

// RPC endpoints for different environments
export const RPC_ENDPOINTS = {
  solana: {
    mainnet: 'https://api.mainnet-beta.solana.com',
    testnet: 'https://api.testnet.solana.com',
    devnet: 'https://api.devnet.solana.com',
  },
  base: {
    mainnet: 'https://mainnet.base.org',
    testnet: 'https://sepolia.base.org',
  },
  arbitrum: {
    mainnet: 'https://arb1.arbitrum.io/rpc',
    testnet: 'https://sepolia-rollup.arbitrum.io/rpc',
  },
};

// Helper functions
export function getChainConfig(chainId: string): ChainConfig | undefined {
  return SUPPORTED_CHAINS[chainId];
}

export function getTokenConfig(symbol: string): TokenConfig | undefined {
  return SUPPORTED_TOKENS[symbol];
}

export function getTokenAddress(tokenSymbol: string, chainId: string): string | undefined {
  const token = getTokenConfig(tokenSymbol);
  return token?.addresses[chainId];
}

export function getSupportedTokensForChain(chainId: string): TokenConfig[] {
  return Object.values(SUPPORTED_TOKENS).filter(token =>
    token.addresses[chainId] !== undefined
  );
}

// Get tokens that are native to a specific chain (for Send/Swap operations)
export function getNativeTokensForChain(chainId: string): TokenConfig[] {
  const nativeTokens: Record<string, string[]> = {
    'solana': ['SOL', 'USDC', 'BONK', 'WIF'], // Solana native SPL tokens
    'ethereum': ['ETH', 'USDC', 'USDT'], // Ethereum native tokens
    'base': ['ETH', 'USDC'], // Base native tokens
    'polygon': ['MATIC', 'USDC', 'USDT'], // Polygon native tokens
    'avalanche': ['AVAX', 'USDC'], // Avalanche native tokens
    'arbitrum': ['ETH', 'USDC'], // Arbitrum native tokens
  };

  const chainNativeTokens = nativeTokens[chainId] || [];
  return Object.values(SUPPORTED_TOKENS).filter(token =>
    chainNativeTokens.includes(token.symbol)
  );
}

export function isTokenSupportedOnChain(tokenSymbol: string, chainId: string): boolean {
  const token = getTokenConfig(tokenSymbol);
  return token ? token.addresses[chainId] !== undefined : false;
}

// Chain ID mappings for different wallet providers
export const EVM_CHAIN_IDS = {
  ethereum: {
    mainnet: 1,
    testnet: 11155111, // Sepolia
  },
  base: {
    mainnet: 8453,
    testnet: 84532,
  },
  arbitrum: {
    mainnet: 42161,
    testnet: 421614,
  },
  polygon: {
    mainnet: 137,
    testnet: 80002, // Amoy
  },
};

export function getEvmChainId(chainId: string): number {
  const chain = chainId as keyof typeof EVM_CHAIN_IDS;
  const network = process.env.NODE_ENV === 'production' ? 'mainnet' : 'testnet';
  return EVM_CHAIN_IDS[chain]?.[network] || 1;
}

export function getEvmChainIdHex(chainId: string): string {
  const numericId = getEvmChainId(chainId);
  return `0x${numericId.toString(16)}`;
}
