import { Connection, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import { Signer } from '@wormhole-foundation/sdk';

// Phantom wallet interface
interface PhantomWallet {
  isPhantom: boolean;
  publicKey: PublicKey | null;
  isConnected: boolean;
  signTransaction<T extends Transaction | VersionedTransaction>(transaction: T): Promise<T>;
  signAllTransactions<T extends Transaction | VersionedTransaction>(transactions: T[]): Promise<T[]>;
  connect(): Promise<{ publicKey: PublicKey }>;
  disconnect(): Promise<void>;
}

declare global {
  interface Window {
    phantom?: {
      solana?: PhantomWallet;
    };
  }
}

export class SolanaWalletSigner implements Signer {
  private wallet: PhantomWallet;
  private connection: Connection;

  constructor(connection: Connection) {
    this.connection = connection;
    
    if (!window.phantom?.solana) {
      throw new Error('Phantom wallet not found');
    }
    
    this.wallet = window.phantom.solana;
    
    if (!this.wallet.isConnected || !this.wallet.publicKey) {
      throw new Error('Phantom wallet not connected');
    }
  }

  async sign(transaction: any): Promise<any> {
    try {
      console.log('🖊️ Signing transaction with Phantom wallet...');
      
      // Convert to the appropriate transaction type
      if (transaction instanceof Transaction) {
        const signed = await this.wallet.signTransaction(transaction);
        console.log('✅ Transaction signed successfully');
        return signed;
      } else if (transaction instanceof VersionedTransaction) {
        const signed = await this.wallet.signTransaction(transaction);
        console.log('✅ Versioned transaction signed successfully');
        return signed;
      } else {
        // Handle other transaction types
        console.log('🔄 Converting transaction for signing...');
        const tx = new Transaction();
        // Add transaction instructions here based on the transaction structure
        const signed = await this.wallet.signTransaction(tx);
        console.log('✅ Converted transaction signed successfully');
        return signed;
      }
    } catch (error) {
      console.error('❌ Failed to sign transaction:', error);
      throw new Error(`Transaction signing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async signAndSend(transaction: any): Promise<string> {
    try {
      console.log('🚀 Signing and sending transaction...');
      
      const signed = await this.sign(transaction);
      
      // Send the signed transaction
      const signature = await this.connection.sendRawTransaction(signed.serialize(), {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
      });
      
      console.log('📡 Transaction sent, signature:', signature);
      
      // Wait for confirmation
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err}`);
      }
      
      console.log('✅ Transaction confirmed:', signature);
      return signature;
      
    } catch (error) {
      console.error('❌ Failed to sign and send transaction:', error);
      throw error;
    }
  }

  getAddress(): string {
    if (!this.wallet.publicKey) {
      throw new Error('Wallet not connected');
    }
    return this.wallet.publicKey.toString();
  }
}

// Factory function to create wallet signers
export async function createWalletSigner(chainId: string): Promise<Signer> {
  if (chainId === 'solana') {
    const connection = new Connection('https://api.devnet.solana.com', 'confirmed');
    return new SolanaWalletSigner(connection);
  } else {
    // For EVM chains, we'd implement similar wallet integration
    // For now, throw an error to indicate it's not implemented
    throw new Error(`Wallet signer not implemented for chain: ${chainId}`);
  }
}

// Helper function to check if wallet is available and connected
export function isWalletConnected(chainId: string): boolean {
  if (chainId === 'solana') {
    return !!(window.phantom?.solana?.isConnected && window.phantom?.solana?.publicKey);
  }
  
  // Add other chain wallet checks here
  return false;
}

// Helper function to get wallet address
export function getWalletAddress(chainId: string): string | null {
  if (chainId === 'solana') {
    return window.phantom?.solana?.publicKey?.toString() || null;
  }
  
  // Add other chain wallet address getters here
  return null;
}
