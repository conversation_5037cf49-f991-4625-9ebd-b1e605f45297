import { wormhole, Wormhole, amount, TokenTransfer } from '@wormhole-foundation/sdk';
import evm from '@wormhole-foundation/sdk/evm';
import solana from '@wormhole-foundation/sdk/solana';
import { WORMHOLE_NETWORK, getChainConfig, getTokenConfig } from './blockchain-config';
import { createWalletSigner } from './wallet-signer';

// Wormhole service class for handling cross-chain operations
export class WormholeService {
  private wh: Wormhole | null = null;
  private initialized = false;
  private balanceCache: Map<string, { balance: string; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds cache
  private lastRpcCall = 0;
  private readonly RPC_DELAY = 1000; // 1 second between calls

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      this.wh = await wormhole(WORMHOLE_NETWORK, [evm, solana]);
      this.initialized = true;
      console.log('Wormhole SDK initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Wormhole SDK:', error);
      throw error;
    }
  }

  async getChainInfo(chainId: string) {
    if (!this.wh) {
      throw new Error('Wormhole service not initialized');
    }

    const config = getChainConfig(chainId);
    if (!config) {
      throw new Error(`Unsupported chain: ${chainId}`);
    }

    try {
      const chain = this.wh.getChain(config.name as any);
      return {
        chainId: chain.config.chainId,
        rpc: chain.config.rpc,
        name: config.name,
      };
    } catch (error) {
      console.error(`Failed to get chain info for ${chainId}:`, error);
      throw error;
    }
  }

  async estimateBridgeFee(
    fromChain: string,
    toChain: string,
    tokenSymbol: string,
    amount: string
  ): Promise<{
    bridgeFee: string;
    networkFee: string;
    totalFee: string;
    estimatedTime: number;
  }> {
    // For now, return mock estimates
    // In a real implementation, this would query the Wormhole network
    const bridgeFeePercent = 0.001; // 0.1%
    const amountNum = parseFloat(amount);
    const bridgeFee = amountNum * bridgeFeePercent;
    const networkFee = 2.5; // Mock network fee in USD

    return {
      bridgeFee: bridgeFee.toFixed(6),
      networkFee: networkFee.toFixed(2),
      totalFee: (bridgeFee + networkFee).toFixed(2),
      estimatedTime: 3, // minutes
    };
  }

  async getTokenBalance(
    chainId: string,
    tokenSymbol: string,
    walletAddress: string
  ): Promise<string> {
    console.log(`wormholeService.getTokenBalance: ${tokenSymbol} on ${chainId} for ${walletAddress}`);

    if (chainId === 'solana') {
      // Use real Solana RPC for balance fetching
      return await this.getSolanaBalance(tokenSymbol, walletAddress);
    }

    // For other chains, use mock data for now
    const mockBalances: Record<string, Record<string, string>> = {
      solana: {
        SOL: '12.5',
        USDC: '1000.00',
        USDT: '500.00',
        ETH: '0.25',
        MATIC: '250.0',
        DAI: '800.0',
        RAY: '150.0',
        SRM: '75.0',
        ORCA: '200.0',
        MNGO: '500.0',
        STEP: '1200.0',
        COPE: '300.0',
        FIDA: '180.0',
        SAMO: '50000.0',
        BONK: '1000000.0',
        WIF: '25.0',
        JUP: '85.0',
        PYTH: '120.0',
        RENDER: '15.0',
        AVAX: '5.2',
        BNB: '2.8',
        stETH: '0.15',
        LDO: '45.0',
        SHIB: '500000.0',
      },
      ethereum: {
        ETH: '2.5',
        USDC: '3000.00',
        USDT: '1200.00',
        DAI: '2500.0',
        WBTC: '0.05',
        UNI: '25.0',
        LINK: '50.0',
        AAVE: '8.0',
        SHIB: '1000000.0',
        PEPE: '500000.0',
        DOGE: '2500.0',
        CRV: '150.0',
        COMP: '3.5',
        MKR: '0.8',
        SNX: '45.0',
        SUSHI: '80.0',
        ARB: '100.0',
        OP: '75.0',
        AVAX: '12.0',
        BNB: '5.5',
        stETH: '1.8',
        LDO: '120.0',
        RENDER: '25.0',
      },
      base: {
        ETH: '0.75',
        USDC: '2000.00',
        USDT: '800.00',
        PEPE: '250000.0',
      },
      arbitrum: {
        ETH: '1.2',
        USDC: '1500.00',
        USDT: '600.00',
        WBTC: '0.03',
        UNI: '15.0',
        LINK: '30.0',
        AAVE: '5.0',
        ARB: '200.0',
      },
      polygon: {
        MATIC: '500.0',
        USDC: '2500.00',
        USDT: '1000.00',
        DAI: '800.0',
        WBTC: '0.02',
        UNI: '20.0',
        LINK: '40.0',
        AAVE: '6.0',
        CRV: '100.0',
        COMP: '2.5',
        SUSHI: '60.0',
      },
    };

    return mockBalances[chainId]?.[tokenSymbol] || '0';
  }

  private async getSolanaBalance(tokenSymbol: string, walletAddress: string): Promise<string> {
    try {
      console.log(`getSolanaBalance: Fetching ${tokenSymbol} for ${walletAddress}`);

      // Check cache first
      const cacheKey = `${walletAddress}-${tokenSymbol}`;
      const cached = this.balanceCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        console.log(`getSolanaBalance: Using cached balance for ${tokenSymbol}: ${cached.balance}`);
        return cached.balance;
      }

      // Rate limiting - wait if we made a call recently
      const now = Date.now();
      const timeSinceLastCall = now - this.lastRpcCall;
      if (timeSinceLastCall < this.RPC_DELAY) {
        const waitTime = this.RPC_DELAY - timeSinceLastCall;
        console.log(`getSolanaBalance: Rate limiting, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      this.lastRpcCall = Date.now();

      // Import Solana web3 dynamically to avoid SSR issues
      const { Connection, PublicKey } = await import('@solana/web3.js');

      // Try multiple RPC endpoints for better reliability
      const rpcEndpoints = [
        'https://api.devnet.solana.com',
        'https://devnet.helius-rpc.com/?api-key=demo',
        'https://solana-devnet.g.alchemy.com/v2/demo',
      ];

      let connection: any = null;
      let lastError: any = null;

      for (const endpoint of rpcEndpoints) {
        try {
          connection = new Connection(endpoint, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 30000,
          });
          // Test the connection with a simple call
          await connection.getSlot();
          console.log(`getSolanaBalance: Using RPC endpoint: ${endpoint}`);
          break;
        } catch (error) {
          console.log(`getSolanaBalance: RPC endpoint ${endpoint} failed:`, error);
          lastError = error;
          connection = null;
        }
      }

      if (!connection) {
        throw new Error(`All RPC endpoints failed. Last error: ${lastError}`);
      }
      const walletPublicKey = new PublicKey(walletAddress);

      let balance = '0';

      if (tokenSymbol === 'SOL') {
        // Get SOL balance
        const lamports = await connection.getBalance(walletPublicKey);
        balance = (lamports / 1e9).toString(); // Convert lamports to SOL
        console.log(`getSolanaBalance: SOL balance: ${balance}`);
      } else if (tokenSymbol === 'USDC') {
        // Get USDC balance using the devnet USDC mint
        const usdcMint = new PublicKey('4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU');

        console.log(`getSolanaBalance: Looking for USDC token accounts...`);

        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
          walletPublicKey,
          { mint: usdcMint }
        );

        console.log(`getSolanaBalance: Found ${tokenAccounts.value.length} USDC token accounts`);

        if (tokenAccounts.value.length > 0) {
          const uiAmount = tokenAccounts.value[0].account.data.parsed.info.tokenAmount.uiAmount;
          balance = uiAmount?.toString() || '0';
          console.log(`getSolanaBalance: USDC balance: ${balance}`);
        } else {
          console.log(`getSolanaBalance: No USDC token accounts found`);
          balance = '0';
        }
      } else {
        // For other SPL tokens, we'd need their mint addresses
        console.log(`getSolanaBalance: ${tokenSymbol} not implemented yet`);
        balance = '0';
      }

      // Cache the result
      this.balanceCache.set(cacheKey, {
        balance,
        timestamp: Date.now(),
      });

      return balance;
    } catch (error) {
      console.error(`getSolanaBalance: Error fetching ${tokenSymbol} balance:`, error);
      return '0';
    }
  }

  async calculateFees(
    fromChain: string,
    toChain: string,
    tokenSymbol: string,
    amount: string
  ): Promise<{ bridgeFee: string; networkFee: string; totalFee: string }> {
    try {
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum) || amountNum <= 0) {
        return { bridgeFee: '0', networkFee: '0', totalFee: '$0.00' };
      }

      // Bridge fee calculation (0.1% of amount)
      const bridgeFeePercent = 0.001;
      const bridgeFee = (amountNum * bridgeFeePercent).toFixed(6);

      // Real network fees based on current gas prices
      let networkFee = '0';
      let networkFeeCurrency = '';

      if (fromChain === 'solana') {
        // Solana transaction fee (approximately 0.000005 SOL)
        networkFee = '0.000005';
        networkFeeCurrency = 'SOL';
      } else {
        // EVM chains - estimate gas fee
        const gasPrice = await this.getGasPrice(fromChain);
        const gasLimit = 150000; // Typical for token bridge
        const gasFeeEth = (gasPrice * gasLimit) / 1e18;
        networkFee = gasFeeEth.toFixed(6);
        networkFeeCurrency = 'ETH';
      }

      // Calculate total fee in USD
      const bridgeFeeUsd = parseFloat(bridgeFee) * 1.0; // Assuming stablecoin
      const networkFeeUsd = await this.convertToUsd(networkFee, networkFeeCurrency);
      const totalFeeUsd = bridgeFeeUsd + networkFeeUsd;

      return {
        bridgeFee: `${bridgeFee} ${tokenSymbol}`,
        networkFee: `${networkFee} ${networkFeeCurrency}`,
        totalFee: `$${totalFeeUsd.toFixed(2)}`,
      };
    } catch (error) {
      console.error('Failed to calculate fees:', error);
      return { bridgeFee: '0', networkFee: '0', totalFee: '$0.00' };
    }
  }

  private async getGasPrice(chainId: string): Promise<number> {
    // Mock gas prices in gwei
    const gasPrices: Record<string, number> = {
      ethereum: 20, // 20 gwei
      base: 0.1,    // 0.1 gwei
      arbitrum: 0.1, // 0.1 gwei
      polygon: 30,   // 30 gwei
    };

    return (gasPrices[chainId] || 20) * 1e9; // Convert to wei
  }

  private async convertToUsd(amount: string, currency: string): Promise<number> {
    // Simple price conversion - in real app, use price service
    const prices: Record<string, number> = {
      SOL: 100,  // $100 per SOL
      ETH: 2500, // $2500 per ETH
    };

    const price = prices[currency] || 0;
    return parseFloat(amount) * price;
  }

  async initiateTransfer(params: {
    fromChain: string;
    toChain: string;
    tokenSymbol: string;
    amount: string;
    recipientAddress: string;
    senderAddress: string;
  }): Promise<{
    transactionId: string;
    status: 'pending' | 'confirmed' | 'failed';
    estimatedCompletion: Date;
  }> {
    if (!this.wh) {
      throw new Error('Wormhole service not initialized');
    }

    // Validate parameters
    const fromChainConfig = getChainConfig(params.fromChain);
    const toChainConfig = getChainConfig(params.toChain);
    const tokenConfig = getTokenConfig(params.tokenSymbol);

    if (!fromChainConfig || !toChainConfig || !tokenConfig) {
      throw new Error('Invalid chain or token configuration');
    }

    try {
      console.log('🌉 Initiating REAL Wormhole bridge transfer:', params);

      // Step 1: Initialize Wormhole if not already done
      await this.initialize();

      // Step 2: Get chain contexts
      const sourceChain = this.wh!.getChain(params.fromChain as any);
      const destChain = this.wh!.getChain(params.toChain as any);

      console.log('📡 Source chain:', sourceChain.chain);
      console.log('🎯 Destination chain:', destChain.chain);

      // Step 3: Get token information
      const tokenConfig = getTokenConfig(params.tokenSymbol);
      if (!tokenConfig) {
        throw new Error(`Token ${params.tokenSymbol} not found`);
      }

      const tokenAddress = tokenConfig.addresses[params.fromChain];
      if (!tokenAddress) {
        throw new Error(`Token ${params.tokenSymbol} not available on ${params.fromChain}`);
      }

      // Step 4: Create token identifier
      const token = Wormhole.tokenId(sourceChain.chain, tokenAddress);
      console.log('🪙 Token ID:', token);

      // Step 5: Parse amount with proper decimals
      const transferAmount = amount.units(
        amount.parse(params.amount, tokenConfig.decimals)
      );
      console.log('💰 Transfer amount:', transferAmount);

      // Step 6: Create transfer request
      const transfer = await sourceChain.getTokenTransfer(
        token,
        transferAmount,
        params.senderAddress,
        params.recipientAddress,
        false // automatic delivery
      );

      console.log('📋 Transfer request created:', transfer);

      // Step 7: Get quote for the transfer
      const quote = await TokenTransfer.quoteTransfer(
        this.wh!,
        sourceChain.chain,
        destChain.chain,
        transfer.transfer
      );

      console.log('💵 Transfer quote:', quote);

      // Step 8: Create wallet signer for source chain
      const signer = await createWalletSigner(params.fromChain);
      console.log('🔑 Wallet signer created for', params.fromChain);

      // Step 9: Execute the transfer (this will require wallet signing)
      console.log('🖊️ Initiating transfer - wallet signature required...');
      const txids = await transfer.initiateTransfer(signer);
      console.log('✅ Transfer initiated! Transaction IDs:', txids);

      // Step 10: Wait for attestation
      console.log('⏳ Waiting for Wormhole attestation...');
      const attestIds = await transfer.fetchAttestation(60_000); // 60 second timeout
      console.log('📜 Attestation received:', attestIds);

      // Step 11: Complete transfer on destination chain
      console.log('🎯 Completing transfer on destination chain...');
      const destSigner = await createWalletSigner(params.toChain);
      const destTxIds = await transfer.completeTransfer(destSigner);
      console.log('🎉 Transfer completed! Destination transaction IDs:', destTxIds);

      return {
        transactionId: txids[0]?.txid || `wh_${Date.now()}`,
        status: 'confirmed' as const,
        estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        sourceTxId: txids[0]?.txid,
        destTxId: destTxIds[0]?.txid,
        attestation: attestIds[0]
      };

    } catch (error) {
      console.error('❌ Failed to execute real bridge transfer:', error);

      // For development, still allow the UI to work with simulation
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 Development mode: Using simulation fallback');
        const mockTxId = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return {
          transactionId: mockTxId,
          status: 'confirmed' as const,
          estimatedCompletion: new Date(Date.now() + 3 * 60 * 1000),
        };
      }

      throw error;
    }
  }

  async getTransferStatus(transactionId: string): Promise<{
    status: 'pending' | 'confirmed' | 'failed';
    sourceChainTx?: string;
    destinationChainTx?: string;
    vaaId?: string;
    progress: number; // 0-100
  }> {
    // Mock implementation
    // In a real app, this would query Wormhole's VAA database
    return {
      status: 'pending',
      progress: 25,
    };
  }

  async getSupportedRoutes(): Promise<Array<{
    fromChain: string;
    toChain: string;
    supportedTokens: string[];
  }>> {
    const routes = [];
    const chains = Object.keys(getChainConfig('solana') ? { solana: true } : {});
    
    // Add all possible chain combinations
    for (const fromChain of ['solana', 'base', 'arbitrum']) {
      for (const toChain of ['solana', 'base', 'arbitrum']) {
        if (fromChain !== toChain) {
          routes.push({
            fromChain,
            toChain,
            supportedTokens: ['USDC', 'USDT'], // Common tokens across chains
          });
        }
      }
    }

    return routes;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  getWormholeInstance(): Wormhole | null {
    return this.wh;
  }
}

// Singleton instance
export const wormholeService = new WormholeService();

// Initialize the service
export async function initializeWormholeService(): Promise<void> {
  await wormholeService.initialize();
}

// Helper function to check if a bridge route is supported
export function isBridgeRouteSupported(fromChain: string, toChain: string, tokenSymbol: string): boolean {
  const fromConfig = getChainConfig(fromChain);
  const toConfig = getChainConfig(toChain);
  const tokenConfig = getTokenConfig(tokenSymbol);

  if (!fromConfig || !toConfig || !tokenConfig) {
    return false;
  }

  // Check if token is supported on both chains
  return (
    tokenConfig.addresses[fromChain] !== undefined &&
    tokenConfig.addresses[toChain] !== undefined
  );
}

// Helper function to format bridge transaction URL
export function getBridgeTransactionUrl(transactionId: string): string {
  return `https://wormholescan.io/#/tx/${transactionId}`;
}
