import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChainLogo } from "@/components/TokenLogo";
import { useBridge } from "@/hooks/useBridge";
import { SUPPORTED_CHAINS } from "@/lib/blockchain-config";
import { formatTokenAmount } from "@/lib/utils";
import { ExternalLink, Clock, CheckCircle, XCircle } from "lucide-react";

export function TransactionHistory() {
  const { transactions } = useBridge();

  if (transactions.length === 0) {
    return (
      <Card className="p-6 bg-gradient-card border-border/50">
        <h3 className="font-semibold text-sm mb-3">Recent Transactions</h3>
        <div className="text-center text-muted-foreground py-8">
          <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No transactions yet</p>
          <p className="text-xs">Your bridge transactions will appear here</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 bg-gradient-card border-border/50">
      <h3 className="font-semibold text-sm mb-4">Recent Transactions</h3>
      <div className="space-y-3">
        {transactions.slice(0, 5).map((tx) => (
          <TransactionItem key={tx.id} transaction={tx} />
        ))}
        {transactions.length > 5 && (
          <Button variant="ghost" className="w-full text-xs">
            View All Transactions
          </Button>
        )}
      </div>
    </Card>
  );
}

interface TransactionItemProps {
  transaction: {
    id: string;
    status: 'pending' | 'confirmed' | 'failed';
    fromChain: string;
    toChain: string;
    token: string;
    amount: string;
    timestamp: Date;
    estimatedCompletion?: Date;
  };
}

function TransactionItem({ transaction }: TransactionItemProps) {
  const fromChain = SUPPORTED_CHAINS[transaction.fromChain];
  const toChain = SUPPORTED_CHAINS[transaction.toChain];

  const getStatusIcon = () => {
    switch (transaction.status) {
      case 'pending':
        return <Clock className="h-3 w-3 text-warning" />;
      case 'confirmed':
        return <CheckCircle className="h-3 w-3 text-success" />;
      case 'failed':
        return <XCircle className="h-3 w-3 text-destructive" />;
    }
  };

  const getStatusColor = () => {
    switch (transaction.status) {
      case 'pending':
        return 'border-warning text-warning';
      case 'confirmed':
        return 'border-success text-success';
      case 'failed':
        return 'border-destructive text-destructive';
    }
  };

  const openTransaction = () => {
    // In a real implementation, this would open the transaction in a block explorer
    const url = `https://wormholescan.io/#/tx/${transaction.id}`;
    window.open(url, '_blank');
  };

  return (
    <div className="flex items-center justify-between p-3 bg-secondary/30 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1">
          {fromChain?.logo && (
            <ChainLogo
              src={fromChain.logo}
              alt={fromChain.name}
              className="w-4 h-4 rounded-full"
            />
          )}
          <span className="text-xs text-muted-foreground">→</span>
          {toChain?.logo && (
            <ChainLogo
              src={toChain.logo}
              alt={toChain.name}
              className="w-4 h-4 rounded-full"
            />
          )}
        </div>
        
        <div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {formatTokenAmount(transaction.amount)} {transaction.token}
            </span>
            <Badge variant="outline" className={`text-xs ${getStatusColor()}`}>
              <span className="mr-1">{getStatusIcon()}</span>
              {transaction.status}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground">
            {transaction.timestamp.toLocaleString()}
          </p>
        </div>
      </div>

      <Button
        variant="ghost"
        size="sm"
        onClick={openTransaction}
        className="h-8 w-8 p-0"
      >
        <ExternalLink className="h-3 w-3" />
      </Button>
    </div>
  );
}

// Component to show transaction status details
export function TransactionStatus({ transactionId }: { transactionId: string }) {
  // This would fetch real transaction status from Wormhole
  const mockStatus = {
    status: 'pending' as const,
    progress: 65,
    steps: [
      { name: 'Transaction Submitted', completed: true },
      { name: 'Source Chain Confirmation', completed: true },
      { name: 'VAA Generation', completed: true },
      { name: 'Destination Chain Processing', completed: false },
      { name: 'Bridge Complete', completed: false },
    ],
  };

  return (
    <Card className="p-4 bg-gradient-card border-border/50">
      <h3 className="font-semibold text-sm mb-3">Transaction Progress</h3>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm">Progress</span>
          <span className="text-sm font-medium">{mockStatus.progress}%</span>
        </div>
        
        <div className="w-full bg-secondary/50 rounded-full h-2">
          <div 
            className="bg-gradient-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${mockStatus.progress}%` }}
          />
        </div>

        <div className="space-y-2">
          {mockStatus.steps.map((step, index) => (
            <div key={index} className="flex items-center gap-2">
              {step.completed ? (
                <CheckCircle className="h-4 w-4 text-success" />
              ) : (
                <div className="h-4 w-4 rounded-full border-2 border-muted" />
              )}
              <span className={`text-sm ${step.completed ? 'text-foreground' : 'text-muted-foreground'}`}>
                {step.name}
              </span>
            </div>
          ))}
        </div>

        <Button variant="outline" className="w-full mt-4" onClick={() => window.open(`https://wormholescan.io/#/tx/${transactionId}`, '_blank')}>
          <ExternalLink className="h-4 w-4 mr-2" />
          View on Wormholescan
        </Button>
      </div>
    </Card>
  );
}
