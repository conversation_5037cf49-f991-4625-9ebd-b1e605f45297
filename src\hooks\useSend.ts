import { useState, useCallback } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { useTokenPrices } from '@/lib/price-service';
import { getTokenConfig, getNativeTokensForChain, SUPPORTED_CHAINS } from '@/lib/blockchain-config';
import { useBridge } from '@/hooks/useBridge';
import { Connection, PublicKey, Transaction, SystemProgram } from '@solana/web3.js';
import { createTransferInstruction, getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from '@solana/spl-token';

interface Recipient {
  address: string;
  amount: string;
}

interface SendState {
  selectedChain: string;
  selectedToken: string;
  recipients: Recipient[];
  isLoading: boolean;
  error: string | null;
}

export function useSend() {
  const { getWalletForChain, isChainConnected } = useWallet();
  const { prices, loading: pricesLoading } = useTokenPrices(['SOL', 'USDC', 'BONK', 'WIF', 'ETH', 'MATIC']);
  const { getTokenBalance: getBridgeTokenBalance } = useBridge(); // Use real balances from bridge

  const [sendState, setSendState] = useState<SendState>({
    selectedChain: 'solana',
    selectedToken: 'SOL',
    recipients: [{ address: '', amount: '' }],
    isLoading: false,
    error: null,
  });

  const updateSendState = useCallback((updates: Partial<SendState>) => {
    setSendState(prev => ({ ...prev, ...updates }));
  }, []);

  const addRecipient = useCallback(() => {
    setSendState(prev => ({
      ...prev,
      recipients: [...prev.recipients, { address: '', amount: '' }]
    }));
  }, []);

  const removeRecipient = useCallback((index: number) => {
    setSendState(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  }, []);

  const updateRecipient = useCallback((index: number, updates: Partial<Recipient>) => {
    setSendState(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) => 
        i === index ? { ...recipient, ...updates } : recipient
      )
    }));
  }, []);

  const getSupportedTokens = useCallback((chainId?: string) => {
    const chain = chainId || sendState.selectedChain;
    return getNativeTokensForChain(chain);
  }, [sendState.selectedChain]);

  const getTokenBalance = useCallback((chainId: string, tokenSymbol: string): string => {
    return getBridgeTokenBalance(chainId, tokenSymbol); // Use real balances
  }, [getBridgeTokenBalance]);

  const getTokenPrice = useCallback((tokenSymbol: string): number => {
    return prices[tokenSymbol] || 0;
  }, [prices]);

  const getUsdValue = useCallback((amount: string, tokenSymbol: string): number => {
    const price = getTokenPrice(tokenSymbol);
    return parseFloat(amount || '0') * price;
  }, [getTokenPrice]);

  const formatUsdValue = useCallback((value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }, []);

  const getTotalAmount = useCallback((): string => {
    const total = sendState.recipients.reduce((sum, recipient) => {
      return sum + parseFloat(recipient.amount || '0');
    }, 0);
    return total.toString();
  }, [sendState.recipients]);

  const getEstimatedFee = useCallback((): string => {
    // Base fee for transaction + fee per recipient
    const baseFee = 0.000005; // 5000 lamports
    const perRecipientFee = 0.000005; // 5000 lamports per recipient
    const totalFee = baseFee + (perRecipientFee * sendState.recipients.length);
    return totalFee.toFixed(6);
  }, [sendState.recipients.length]);

  const validateSend = useCallback(() => {
    if (sendState.recipients.length === 0) {
      return { isValid: false, error: 'Add at least one recipient' };
    }

    for (let i = 0; i < sendState.recipients.length; i++) {
      const recipient = sendState.recipients[i];
      
      if (!recipient.address) {
        return { isValid: false, error: `Enter address for recipient #${i + 1}` };
      }

      if (!recipient.amount || parseFloat(recipient.amount) <= 0) {
        return { isValid: false, error: `Enter amount for recipient #${i + 1}` };
      }

      try {
        new PublicKey(recipient.address);
      } catch {
        return { isValid: false, error: `Invalid address for recipient #${i + 1}` };
      }
    }

    const totalAmount = getTotalAmount();
    const balance = parseFloat(getTokenBalance(sendState.selectedChain, sendState.selectedToken));

    if (parseFloat(totalAmount) > balance) {
      return { isValid: false, error: `Insufficient ${sendState.selectedToken} balance` };
    }

    const wallet = getWalletForChain(sendState.selectedChain);
    if (!wallet.isConnected) {
      return { isValid: false, error: `Connect ${SUPPORTED_CHAINS[sendState.selectedChain]?.name || sendState.selectedChain} wallet` };
    }

    return { isValid: true };
  }, [sendState, getTotalAmount, getTokenBalance, getWalletForChain]);

  const executeSend = useCallback(async () => {
    const validation = validateSend();
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    setSendState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('📤 Executing multi-send:', {
        chain: sendState.selectedChain,
        token: sendState.selectedToken,
        recipients: sendState.recipients.length,
        totalAmount: getTotalAmount(),
      });

      // In a real implementation, this would:
      // 1. Create a single transaction with multiple transfer instructions
      // 2. For SOL: Use SystemProgram.transfer for each recipient
      // 3. For SPL tokens: Use createTransferInstruction for each recipient
      // 4. Sign and send the batch transaction

      const connection = new Connection('https://api.devnet.solana.com');
      
      // Simulate batch transaction creation
      console.log('🔨 Creating batch transaction...');
      
      // Mock transaction building
      for (let i = 0; i < sendState.recipients.length; i++) {
        const recipient = sendState.recipients[i];
        console.log(`📋 Adding transfer instruction ${i + 1}:`, {
          to: recipient.address,
          amount: `${recipient.amount} ${sendState.selectedToken}`
        });
      }

      // Simulate transaction signing and sending
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock transaction result
      const txSignature = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      console.log('✅ Batch send completed:', txSignature);
      console.log(`📊 Sent to ${sendState.recipients.length} recipients with single gas fee`);

      // Reset form
      setSendState(prev => ({
        ...prev,
        recipients: [{ address: '', amount: '' }],
        isLoading: false,
      }));

      return {
        signature: txSignature,
        recipientCount: sendState.recipients.length,
        totalAmount: getTotalAmount(),
        token: sendState.selectedToken,
        estimatedFee: getEstimatedFee(),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Send failed';
      setSendState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false,
      }));
      throw error;
    }
  }, [sendState, validateSend, getTotalAmount, getEstimatedFee]);

  return {
    // State
    sendState,
    pricesLoading,
    
    // Actions
    updateSendState,
    addRecipient,
    removeRecipient,
    updateRecipient,
    executeSend,
    
    // Utilities
    validateSend,
    getSupportedTokens,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getTotalAmount,
    getEstimatedFee,
  };
}
