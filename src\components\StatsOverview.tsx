import { Card } from "@/components/ui/card";
import { TrendingUp, DollarSign, Shield, Clock } from "lucide-react";

const stats = [
  {
    title: "Total Volume",
    value: "$2.4B",
    change: "+12.3%",
    icon: DollarSign,
    color: "text-primary"
  },
  {
    title: "Transactions",
    value: "847K",
    change: "+8.7%",
    icon: TrendingUp,
    color: "text-accent"
  },
  {
    title: "Security Score",
    value: "99.9%",
    change: "Stable",
    icon: Shield,
    color: "text-success"
  },
  {
    title: "Avg Bridge Time",
    value: "2.8 min",
    change: "-15%",
    icon: Clock,
    color: "text-warning"
  }
];

export const StatsOverview = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {stats.map((stat) => (
        <Card key={stat.title} className="p-4 bg-gradient-card border-border/50 hover:border-primary/20 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">{stat.title}</p>
              <p className="text-2xl font-bold">{stat.value}</p>
              <p className={`text-xs ${stat.color}`}>{stat.change}</p>
            </div>
            <div className={`${stat.color} opacity-70`}>
              <stat.icon className="h-8 w-8" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};