import { Badge } from "@/components/ui/badge";
import { WalletConnection } from "@/components/WalletConnection";
import { useWallet } from "@/contexts/WalletContext";
import { Shield, Zap, Globe } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

export const Header = () => {
  const { solanaWallet, evmWallet } = useWallet();
  const location = useLocation();

  // Determine which wallet to show based on connection status
  const getActiveWallet = () => {
    if (solanaWallet.isConnected) return { chainId: 'solana', wallet: solanaWallet };
    if (evmWallet.isConnected) return { chainId: evmWallet.chainId || 'base', wallet: evmWallet };
    return { chainId: 'solana', wallet: null }; // Default to Solana
  };

  const { chainId } = getActiveWallet();

  return (
    <header className="w-full border-b border-border/50 bg-card/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-primary flex items-center justify-center">
              <Globe className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                SolBridge
              </h1>
              <p className="text-xs text-muted-foreground">
                Cross-Chain Bridge
              </p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                location.pathname === '/' ? 'text-primary' : 'text-muted-foreground'
              }`}
            >
              Bridge
            </Link>
            <Link
              to="/swap"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                location.pathname === '/swap' ? 'text-primary' : 'text-muted-foreground'
              }`}
            >
              Swap
            </Link>
            <Link
              to="/send"
              className={`text-sm font-medium transition-colors hover:text-primary ${
                location.pathname === '/send' ? 'text-primary' : 'text-muted-foreground'
              }`}
            >
              Send
            </Link>
          </nav>

          {/* Features */}
          <div className="hidden md:flex items-center gap-4">
            <Badge variant="outline" className="border-primary/20 text-primary">
              <Shield className="h-3 w-3 mr-1" />
              Security First
            </Badge>
            <Badge variant="outline" className="border-accent/20 text-accent">
              <Zap className="h-3 w-3 mr-1" />
              Token-2022
            </Badge>
          </div>

          {/* Wallet Connection */}
          <WalletConnection chainId={chainId} />
        </div>
      </div>
    </header>
  );
};