import { useEffect, useRef } from 'react';
import { wormholeService } from '@/lib/wormhole-service';
import { toast } from 'sonner';

export interface TransactionMonitor {
  id: string;
  status: 'pending' | 'confirmed' | 'failed';
  lastChecked: Date;
  retryCount: number;
}

export function useTransactionMonitor(transactions: Array<{ id: string; status: string }>) {
  const monitorsRef = useRef<Map<string, TransactionMonitor>>(new Map());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Add new pending transactions to monitor
    transactions.forEach(tx => {
      if (tx.status === 'pending' && !monitorsRef.current.has(tx.id)) {
        monitorsRef.current.set(tx.id, {
          id: tx.id,
          status: 'pending',
          lastChecked: new Date(),
          retryCount: 0,
        });
      }
    });

    // Start monitoring if we have pending transactions
    const pendingTransactions = Array.from(monitorsRef.current.values())
      .filter(monitor => monitor.status === 'pending');

    if (pendingTransactions.length > 0 && !intervalRef.current) {
      startMonitoring();
    } else if (pendingTransactions.length === 0 && intervalRef.current) {
      stopMonitoring();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [transactions]);

  const startMonitoring = () => {
    intervalRef.current = setInterval(async () => {
      const pendingMonitors = Array.from(monitorsRef.current.values())
        .filter(monitor => monitor.status === 'pending');

      for (const monitor of pendingMonitors) {
        try {
          const status = await wormholeService.getTransferStatus(monitor.id);
          
          if (status.status !== monitor.status) {
            // Status changed, update monitor and show notification
            monitor.status = status.status;
            monitor.lastChecked = new Date();
            
            showStatusNotification(monitor.id, status.status);
            
            // Remove from monitoring if completed or failed
            if (status.status === 'confirmed' || status.status === 'failed') {
              monitorsRef.current.delete(monitor.id);
            }
          } else {
            monitor.lastChecked = new Date();
            monitor.retryCount++;
            
            // Stop monitoring after too many retries (30 minutes)
            if (monitor.retryCount > 60) {
              monitorsRef.current.delete(monitor.id);
              showTimeoutNotification(monitor.id);
            }
          }
        } catch (error) {
          console.error(`Failed to check status for transaction ${monitor.id}:`, error);
          monitor.retryCount++;
          
          if (monitor.retryCount > 10) {
            monitorsRef.current.delete(monitor.id);
          }
        }
      }

      // Stop monitoring if no pending transactions
      if (pendingMonitors.length === 0) {
        stopMonitoring();
      }
    }, 30000); // Check every 30 seconds
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const showStatusNotification = (transactionId: string, status: string) => {
    const shortId = transactionId.slice(0, 8) + '...';
    
    switch (status) {
      case 'confirmed':
        toast.success(`Bridge transaction confirmed!`, {
          description: `Transaction ${shortId} completed successfully`,
          action: {
            label: 'View',
            onClick: () => window.open(`https://wormholescan.io/#/tx/${transactionId}`, '_blank'),
          },
        });
        break;
      case 'failed':
        toast.error(`Bridge transaction failed`, {
          description: `Transaction ${shortId} failed to complete`,
          action: {
            label: 'View',
            onClick: () => window.open(`https://wormholescan.io/#/tx/${transactionId}`, '_blank'),
          },
        });
        break;
    }
  };

  const showTimeoutNotification = (transactionId: string) => {
    const shortId = transactionId.slice(0, 8) + '...';
    toast.warning(`Transaction monitoring timeout`, {
      description: `Stopped monitoring ${shortId} - please check manually`,
      action: {
        label: 'View',
        onClick: () => window.open(`https://wormholescan.io/#/tx/${transactionId}`, '_blank'),
      },
    });
  };

  return {
    activeMonitors: Array.from(monitorsRef.current.values()),
    isMonitoring: intervalRef.current !== null,
  };
}

// Hook for individual transaction monitoring
export function useTransactionStatus(transactionId: string | null) {
  const statusRef = useRef<{
    status: 'pending' | 'confirmed' | 'failed';
    progress: number;
    lastUpdated: Date;
  } | null>(null);

  useEffect(() => {
    if (!transactionId) return;

    let intervalId: NodeJS.Timeout;
    
    const checkStatus = async () => {
      try {
        const status = await wormholeService.getTransferStatus(transactionId);
        statusRef.current = {
          status: status.status,
          progress: status.progress,
          lastUpdated: new Date(),
        };
      } catch (error) {
        console.error('Failed to check transaction status:', error);
      }
    };

    // Check immediately
    checkStatus();

    // Then check every 30 seconds if still pending
    intervalId = setInterval(() => {
      if (statusRef.current?.status === 'pending') {
        checkStatus();
      } else {
        clearInterval(intervalId);
      }
    }, 30000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [transactionId]);

  return statusRef.current;
}

// Hook for network status monitoring
export function useNetworkStatus() {
  const statusRef = useRef<{
    solana: 'online' | 'degraded' | 'offline';
    base: 'online' | 'degraded' | 'offline';
    arbitrum: 'online' | 'degraded' | 'offline';
    wormhole: 'online' | 'degraded' | 'offline';
    lastChecked: Date;
  }>({
    solana: 'online',
    base: 'online',
    arbitrum: 'online',
    wormhole: 'online',
    lastChecked: new Date(),
  });

  useEffect(() => {
    const checkNetworkStatus = async () => {
      try {
        // In a real implementation, this would check actual network status
        // For now, we'll assume all networks are online
        statusRef.current = {
          solana: 'online',
          base: 'online',
          arbitrum: 'online',
          wormhole: 'online',
          lastChecked: new Date(),
        };
      } catch (error) {
        console.error('Failed to check network status:', error);
      }
    };

    // Check every 5 minutes
    const intervalId = setInterval(checkNetworkStatus, 5 * 60 * 1000);
    checkNetworkStatus(); // Check immediately

    return () => clearInterval(intervalId);
  }, []);

  return statusRef.current;
}

// Error handling and retry logic
export function useErrorHandler() {
  const retryTransaction = async (transactionId: string) => {
    try {
      // In a real implementation, this might attempt to retry a failed transaction
      // or provide guidance on manual intervention
      toast.info('Transaction retry initiated', {
        description: 'Attempting to process transaction again...',
      });
    } catch (error) {
      toast.error('Retry failed', {
        description: 'Unable to retry transaction. Please try again manually.',
      });
    }
  };

  const reportIssue = (transactionId: string, error: string) => {
    // In a real implementation, this would report the issue to support
    toast.info('Issue reported', {
      description: 'Your issue has been reported to our support team.',
    });
  };

  return {
    retryTransaction,
    reportIssue,
  };
}
