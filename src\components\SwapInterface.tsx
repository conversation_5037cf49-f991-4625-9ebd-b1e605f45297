import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { WalletConnection } from "@/components/WalletConnection";
import { TokenSelector } from "@/components/TokenSelector";
import { RecipientAddressInput } from "@/components/RecipientAddressInput";
import { TokenLogo } from "@/components/TokenLogo";
import { useWallet } from "@/contexts/WalletContext";
import { useSwap } from "@/hooks/useSwap";
import { SUPPORTED_CHAINS, getSupportedTokensForChain } from "@/lib/blockchain-config";
import { formatTokenAmount } from "@/lib/utils";
import { ArrowUpDown, Zap, RefreshCw } from "lucide-react";

export const SwapInterface = () => {
  const { isChainConnected } = useWallet();
  const {
    swapState,
    updateSwapState,
    executeSwap,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getSupportedTokens,
    getSwapQuote,
    swapQuote,
    isLoadingQuote,
    validateSwap,
  } = useSwap();

  const supportedTokens = getSupportedTokens();
  const fromTokenBalance = getTokenBalance('solana', swapState.fromToken);
  const fromTokenPrice = getTokenPrice(swapState.fromToken);
  const toTokenPrice = getTokenPrice(swapState.toToken);
  const fromAmountUsd = getUsdValue(swapState.amount, swapState.fromToken);
  const validation = validateSwap();

  // Get quote when amount or tokens change
  useEffect(() => {
    if (swapState.amount && parseFloat(swapState.amount) > 0) {
      getSwapQuote();
    }
  }, [swapState.amount, swapState.fromToken, swapState.toToken]);

  const handleSwapTokens = () => {
    updateSwapState({
      fromToken: swapState.toToken,
      toToken: swapState.fromToken,
      amount: '',
    });
  };

  const setMaxAmount = () => {
    updateSwapState({ amount: fromTokenBalance });
  };

  const handleSwap = async () => {
    if (!validation.isValid) return;
    
    try {
      await executeSwap();
    } catch (error) {
      console.error('Swap failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">Token Swap</h1>
        <p className="text-muted-foreground">
          Swap tokens on Solana and send to any wallet
        </p>
      </div>

      <Card className="p-6 bg-card/50 backdrop-blur-sm border-border/50">
        <div className="space-y-6">
          {/* From Token Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">From</label>
              <div className="text-right">
                <div className="text-xs text-muted-foreground">
                  Balance: {formatTokenAmount(fromTokenBalance)} {swapState.fromToken}
                </div>
                {fromTokenBalance !== '0' && fromTokenPrice > 0 && (
                  <div className="text-xs text-muted-foreground">
                    ≈ {formatUsdValue(getUsdValue(fromTokenBalance, swapState.fromToken))}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <TokenSelector
                tokens={supportedTokens}
                selectedToken={swapState.fromToken}
                onTokenSelect={(token) => updateSwapState({ fromToken: token })}
                getTokenBalance={(token) => getTokenBalance('solana', token)}
                chainId="solana"
                mode="native"
              />

              <div className="relative">
                <Input
                  type="number"
                  placeholder="0.00"
                  value={swapState.amount}
                  onChange={(e) => updateSwapState({ amount: e.target.value })}
                  className="bg-secondary/50 border-border/50 pr-16"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={setMaxAmount}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs text-primary hover:text-primary/80"
                >
                  MAX
                </Button>
              </div>

              {swapState.amount && (
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>≈ {formatUsdValue(fromAmountUsd)}</span>
                  {fromTokenPrice > 0 && (
                    <span>${fromTokenPrice.toFixed(4)} per {swapState.fromToken}</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSwapTokens}
              className="rounded-full border-primary/20 hover:border-primary/40 transition-all duration-300"
            >
              <ArrowUpDown className="h-4 w-4" />
            </Button>
          </div>

          {/* To Token Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium">To</label>
            
            <div className="space-y-2">
              <TokenSelector
                tokens={supportedTokens}
                selectedToken={swapState.toToken}
                onTokenSelect={(token) => updateSwapState({ toToken: token })}
                getTokenBalance={(token) => getTokenBalance('solana', token)}
                chainId="solana"
                mode="native"
              />

              <div className="p-3 bg-secondary/30 rounded border border-border/50">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">You'll receive</span>
                  {isLoadingQuote && (
                    <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                  )}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <TokenLogo 
                    src={supportedTokens.find(t => t.symbol === swapState.toToken)?.logoUrl || ''} 
                    alt={swapState.toToken}
                    className="w-5 h-5"
                    fallbackText={swapState.toToken}
                  />
                  <span className="font-medium">
                    {swapQuote?.outAmount ? formatTokenAmount(swapQuote.outAmount) : '0.00'} {swapState.toToken}
                  </span>
                </div>
                {swapQuote?.outAmount && toTokenPrice > 0 && (
                  <div className="text-xs text-muted-foreground mt-1">
                    ≈ {formatUsdValue(getUsdValue(swapQuote.outAmount, swapState.toToken))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Recipient Address */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Send To</label>
            <RecipientAddressInput 
              chainId="solana"
              onAddressChange={(address) => updateSwapState({ recipientAddress: address })}
            />
          </div>

          {/* Swap Quote Details */}
          {swapQuote && (
            <div className="p-3 bg-secondary/30 rounded-lg border border-border/50">
              <h3 className="text-sm font-medium mb-2 text-foreground">Swap Details</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Rate:</span>
                  <span className="text-foreground">
                    1 {swapState.fromToken} = {swapQuote.rate} {swapState.toToken}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Price Impact:</span>
                  <span className={`text-foreground ${parseFloat(swapQuote.priceImpact) > 5 ? 'text-destructive' : ''}`}>
                    {swapQuote.priceImpact}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network Fee:</span>
                  <span className="text-foreground">{swapQuote.fee} SOL</span>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {!validation.isValid && validation.error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">{validation.error}</p>
            </div>
          )}

          {/* Wallet Connection */}
          {!isChainConnected('solana') && (
            <div className="text-center">
              <WalletConnection chainId="solana" className="w-full" />
            </div>
          )}

          {/* Swap Button */}
          <Button
            onClick={handleSwap}
            disabled={!validation.isValid || !swapQuote}
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            size="lg"
          >
            <Zap className="h-4 w-4 mr-2" />
            {!validation.isValid ? 'Enter Details' : 'Swap Tokens'}
          </Button>
        </div>
      </Card>
    </div>
  );
};
