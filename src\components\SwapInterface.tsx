import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { WalletConnection } from "@/components/WalletConnection";
import { TokenSelector } from "@/components/TokenSelector";
import { RecipientAddressInput } from "@/components/RecipientAddressInput";
import { ChainLogo, TokenLogo } from "@/components/TokenLogo";
import { useWallet } from "@/contexts/WalletContext";
import { useSwap } from "@/hooks/useSwap";
import { SUPPORTED_CHAINS, getSupportedTokensForChain } from "@/lib/blockchain-config";
import { formatTokenAmount } from "@/lib/utils";
import { ArrowUpDown, Zap, RefreshCw } from "lucide-react";

export const SwapInterface = () => {
  const { isChainConnected } = useWallet();
  const {
    swapState,
    updateSwapState,
    executeSwap,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getSupportedTokens,
    getSwapQuote,
    swapQuote,
    isLoadingQuote,
    validateSwap,
  } = useSwap();

  const supportedTokens = getSupportedTokens(swapState.selectedChain);
  const fromTokenBalance = getTokenBalance(swapState.selectedChain, swapState.fromToken);
  const fromTokenPrice = getTokenPrice(swapState.fromToken);
  const toTokenPrice = getTokenPrice(swapState.toToken);
  const fromAmountUsd = getUsdValue(swapState.amount, swapState.fromToken);
  const validation = validateSwap();
  const selectedChainConfig = SUPPORTED_CHAINS[swapState.selectedChain];

  // Update tokens when chain changes and current tokens are not supported
  useEffect(() => {
    const chainTokens = getSupportedTokens(swapState.selectedChain);
    const isFromTokenSupported = chainTokens.some(token => token.symbol === swapState.fromToken);
    const isToTokenSupported = chainTokens.some(token => token.symbol === swapState.toToken);

    if (chainTokens.length > 1) {
      const updates: any = {};
      if (!isFromTokenSupported) {
        updates.fromToken = chainTokens[0].symbol;
      }
      if (!isToTokenSupported || swapState.fromToken === swapState.toToken) {
        updates.toToken = chainTokens[1]?.symbol || chainTokens[0].symbol;
      }
      if (Object.keys(updates).length > 0) {
        updateSwapState(updates);
      }
    }
  }, [swapState.selectedChain, swapState.fromToken, swapState.toToken, getSupportedTokens, updateSwapState]);

  // Get quote when amount or tokens change
  useEffect(() => {
    if (swapState.amount && parseFloat(swapState.amount) > 0) {
      getSwapQuote();
    }
  }, [swapState.amount, swapState.fromToken, swapState.toToken]);

  const handleSwapTokens = () => {
    updateSwapState({
      fromToken: swapState.toToken,
      toToken: swapState.fromToken,
      amount: '',
    });
  };

  const setMaxAmount = () => {
    updateSwapState({ amount: fromTokenBalance });
  };

  const handleSwap = async () => {
    if (!validation.isValid) return;
    
    try {
      await executeSwap();
    } catch (error) {
      console.error('Swap failed:', error);
    }
  };

  return (
    <div className="w-full max-w-lg mx-auto space-y-4">
      <Card className="p-4 bg-gradient-card border-border/50 shadow-elevated">
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center">
            <h2 className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Multi-Chain Swap
            </h2>
            <p className="text-muted-foreground text-xs mt-1">
              Swap tokens and send to any wallet
            </p>
          </div>

          {/* Network Selection */}
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Network</label>
              <Select
                value={swapState.selectedChain}
                onValueChange={(value) => {
                  const newSupportedTokens = getSupportedTokens(value);
                  updateSwapState({
                    selectedChain: value,
                    fromToken: newSupportedTokens[0]?.symbol || 'SOL',
                    toToken: newSupportedTokens[1]?.symbol || 'USDC'
                  });
                }}
              >
                <SelectTrigger className="bg-secondary/50 border-border/50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(SUPPORTED_CHAINS).map((chain) => (
                    <SelectItem key={chain.id} value={chain.id}>
                      <div className="flex items-center gap-2">
                        <ChainLogo
                          src={chain.logo}
                          alt={chain.name}
                          className="w-5 h-5 rounded-full"
                        />
                        <span>{chain.name}</span>
                        {isChainConnected(chain.id) && (
                          <Badge variant="outline" className="border-success text-success text-xs ml-auto">
                            Connected
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* From Token Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">From</label>
              <div className="text-right">
                <div className="text-xs text-muted-foreground">
                  Balance: {formatTokenAmount(fromTokenBalance)} {swapState.fromToken}
                </div>
                {fromTokenBalance !== '0' && fromTokenPrice > 0 && (
                  <div className="text-xs text-muted-foreground">
                    ≈ {formatUsdValue(getUsdValue(fromTokenBalance, swapState.fromToken))}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <TokenSelector
                tokens={supportedTokens}
                selectedToken={swapState.fromToken}
                onTokenSelect={(token) => updateSwapState({ fromToken: token })}
                getTokenBalance={(token) => getTokenBalance(swapState.selectedChain, token)}
                chainId={swapState.selectedChain}
                mode="native"
              />

              <div className="relative">
                <Input
                  type="number"
                  placeholder="0.00"
                  value={swapState.amount}
                  onChange={(e) => updateSwapState({ amount: e.target.value })}
                  className="bg-secondary/50 border-border/50 pr-16"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={setMaxAmount}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs text-primary hover:text-primary/80"
                >
                  MAX
                </Button>
              </div>

              {swapState.amount && (
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>≈ {formatUsdValue(fromAmountUsd)}</span>
                  {fromTokenPrice > 0 && (
                    <span>${fromTokenPrice.toFixed(4)} per {swapState.fromToken}</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSwapTokens}
              className="rounded-full border-primary/20 hover:border-primary/40 transition-all duration-300"
            >
              <ArrowUpDown className="h-4 w-4" />
            </Button>
          </div>

          {/* To Token Section */}
          <div className="space-y-3">
            <label className="text-sm font-medium">To</label>
            
            <div className="space-y-2">
              <TokenSelector
                tokens={supportedTokens}
                selectedToken={swapState.toToken}
                onTokenSelect={(token) => updateSwapState({ toToken: token })}
                getTokenBalance={(token) => getTokenBalance(swapState.selectedChain, token)}
                chainId={swapState.selectedChain}
                mode="native"
              />

              <div className="p-3 bg-secondary/30 rounded border border-border/50">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">You'll receive</span>
                  {isLoadingQuote && (
                    <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
                  )}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <TokenLogo 
                    src={supportedTokens.find(t => t.symbol === swapState.toToken)?.logoUrl || ''} 
                    alt={swapState.toToken}
                    className="w-5 h-5"
                    fallbackText={swapState.toToken}
                  />
                  <span className="font-medium">
                    {swapQuote?.outAmount ? formatTokenAmount(swapQuote.outAmount) : '0.00'} {swapState.toToken}
                  </span>
                </div>
                {swapQuote?.outAmount && toTokenPrice > 0 && (
                  <div className="text-xs text-muted-foreground mt-1">
                    ≈ {formatUsdValue(getUsdValue(swapQuote.outAmount, swapState.toToken))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Recipient Address */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Send To</label>
            <RecipientAddressInput
              chainId={swapState.selectedChain}
              onAddressChange={(address) => updateSwapState({ recipientAddress: address })}
            />
          </div>

          {/* Swap Quote Details */}
          {swapQuote && (
            <div className="p-3 bg-secondary/30 rounded-lg border border-border/50">
              <h3 className="text-sm font-medium mb-2 text-foreground">Swap Details</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Rate:</span>
                  <span className="text-foreground">
                    1 {swapState.fromToken} = {swapQuote.rate} {swapState.toToken}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Price Impact:</span>
                  <span className={`text-foreground ${parseFloat(swapQuote.priceImpact) > 5 ? 'text-destructive' : ''}`}>
                    {swapQuote.priceImpact}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network Fee:</span>
                  <span className="text-foreground">{swapQuote.fee} {selectedChainConfig?.nativeCurrency.symbol || 'SOL'}</span>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {!validation.isValid && validation.error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">{validation.error}</p>
            </div>
          )}

          {/* Wallet Connection */}
          {!isChainConnected(swapState.selectedChain) && (
            <div className="text-center">
              <WalletConnection chainId={swapState.selectedChain} className="w-full" />
            </div>
          )}

          {/* Swap Button */}
          <Button
            onClick={handleSwap}
            disabled={!validation.isValid || !swapQuote}
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            size="lg"
          >
            <Zap className="h-4 w-4 mr-2" />
            {!validation.isValid ? 'Enter Details' : 'Swap Tokens'}
          </Button>
        </div>
      </Card>
    </div>
  );
};
