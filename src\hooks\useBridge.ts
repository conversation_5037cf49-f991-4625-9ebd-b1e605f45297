import { useState, useEffect, useCallback } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { wormholeService } from '@/lib/wormhole-service';
import { priceService, useTokenPrices } from '@/lib/price-service';
import { getTokenConfig, getSupportedTokensForChain, isTokenSupportedOnChain } from '@/lib/blockchain-config';
import { useTransactionMonitor } from '@/hooks/useTransactionMonitor';

export interface BridgeState {
  fromChain: string;
  toChain: string;
  selectedToken: string;
  amount: string;
  recipientAddress: string;
  isLoading: boolean;
  error: string | null;
  fees: {
    bridgeFee: string;
    networkFee: string;
    totalFee: string;
  } | null;
  estimatedTime: number;
}

export interface BridgeTransaction {
  id: string;
  status: 'pending' | 'confirmed' | 'failed';
  fromChain: string;
  toChain: string;
  token: string;
  amount: string;
  timestamp: Date;
  estimatedCompletion?: Date;
}

export function useBridge() {
  const { getWalletFor<PERSON>hain, isChainConnected } = useWallet();
  
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    fromChain: 'solana',
    toChain: 'base',
    selectedToken: 'USDC',
    amount: '',
    recipientAddress: '',
    isLoading: false,
    error: null,
    fees: null,
    estimatedTime: 3,
  });

  // Removed transactions - not needed for this implementation
  // Load balances from localStorage on init
  const [balances, setBalances] = useState<Record<string, Record<string, string>>>(() => {
    try {
      const saved = localStorage.getItem('bridge-balances');
      return saved ? JSON.parse(saved) : {};
    } catch {
      return {};
    }
  });

  // Removed transaction monitoring - not needed

  // Get price data for supported tokens
  const supportedTokenSymbols = getSupportedTokensForChain(bridgeState.fromChain).map(t => t.symbol);
  const { prices, loading: pricesLoading } = useTokenPrices(supportedTokenSymbols);

  // Load balances for connected wallets
  const loadBalances = useCallback(async () => {
    console.log('loadBalances: Starting balance fetch...');
    const chains = ['solana', 'ethereum', 'base', 'arbitrum', 'polygon'];
    const newBalances: Record<string, Record<string, string>> = {};

    for (const chainId of chains) {
      console.log(`loadBalances: Checking ${chainId}, connected:`, isChainConnected(chainId));

      if (isChainConnected(chainId)) {
        const wallet = getWalletForChain(chainId);
        console.log(`loadBalances: ${chainId} wallet:`, wallet);

        if (wallet.address) {
          const supportedTokens = getSupportedTokensForChain(chainId);
          newBalances[chainId] = {};

          console.log(`loadBalances: Fetching balances for ${supportedTokens.length} tokens on ${chainId}`);

          for (const token of supportedTokens) {
            try {
              console.log(`loadBalances: Fetching ${token.symbol} balance for ${wallet.address} on ${chainId}`);

              // For now, let's use the wormhole service for balance fetching
              const balance = await wormholeService.getTokenBalance(
                chainId,
                token.symbol,
                wallet.address
              );

              newBalances[chainId][token.symbol] = balance;
              console.log(`loadBalances: ${token.symbol} balance: ${balance}`);
            } catch (error) {
              console.error(`Failed to load ${token.symbol} balance for ${chainId}:`, error);
              newBalances[chainId][token.symbol] = '0';
            }
          }
        }
      }
    }

    console.log('loadBalances: Final balances:', newBalances);
    setBalances(newBalances);

    // Save to localStorage
    try {
      localStorage.setItem('bridge-balances', JSON.stringify(newBalances));
    } catch (error) {
      console.error('Failed to save balances to localStorage:', error);
    }
  }, [isChainConnected, getWalletForChain]);

  // Load balances when wallets connect
  useEffect(() => {
    loadBalances();
  }, [loadBalances]);

  // Calculate fees when bridge parameters change
  useEffect(() => {
    if (bridgeState.amount && parseFloat(bridgeState.amount) > 0) {
      calculateFees();
    } else {
      setBridgeState(prev => ({ ...prev, fees: null }));
    }
  }, [bridgeState.fromChain, bridgeState.toChain, bridgeState.selectedToken, bridgeState.amount]);

  const calculateFees = async () => {
    try {
      setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const fees = await wormholeService.estimateBridgeFee(
        bridgeState.fromChain,
        bridgeState.toChain,
        bridgeState.selectedToken,
        bridgeState.amount
      );

      setBridgeState(prev => ({
        ...prev,
        fees: {
          bridgeFee: fees.bridgeFee,
          networkFee: fees.networkFee,
          totalFee: fees.totalFee,
        },
        estimatedTime: fees.estimatedTime,
        isLoading: false,
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: 'Failed to calculate fees',
        isLoading: false,
      }));
    }
  };

  const updateBridgeState = (updates: Partial<BridgeState>) => {
    setBridgeState(prev => {
      const newState = { ...prev, ...updates };

      // If chain changed, check if current token is still supported
      if (updates.fromChain && updates.fromChain !== prev.fromChain) {
        const newChainTokens = getSupportedTokensForChain(updates.fromChain);
        const isTokenSupported = newChainTokens.some(token => token.symbol === prev.selectedToken);

        if (!isTokenSupported) {
          // Switch to USDC if available, otherwise first available token
          const usdcAvailable = newChainTokens.some(token => token.symbol === 'USDC');
          newState.selectedToken = usdcAvailable ? 'USDC' : newChainTokens[0]?.symbol || 'USDC';
        }
      }

      return newState;
    });
  };

  const swapChains = () => {
    setBridgeState(prev => ({
      ...prev,
      fromChain: prev.toChain,
      toChain: prev.fromChain,
    }));
  };

  const setMaxAmount = () => {
    const balance = balances[bridgeState.fromChain]?.[bridgeState.selectedToken];
    if (balance) {
      setBridgeState(prev => ({ ...prev, amount: balance }));
    }
  };

  const validateBridge = (): { isValid: boolean; error?: string } => {
    // Check if source wallet is connected
    if (!isChainConnected(bridgeState.fromChain)) {
      return { isValid: false, error: `Please connect your wallet for ${bridgeState.fromChain}` };
    }

    // Check if amount is valid
    const amount = parseFloat(bridgeState.amount);
    if (!amount || amount <= 0) {
      return { isValid: false, error: 'Please enter a valid amount' };
    }

    // Check if user has sufficient balance
    const balance = parseFloat(balances[bridgeState.fromChain]?.[bridgeState.selectedToken] || '0');
    if (amount > balance) {
      return { isValid: false, error: 'Insufficient balance' };
    }

    // Check if recipient address is provided
    if (!bridgeState.recipientAddress) {
      return { isValid: false, error: 'Please provide a recipient address' };
    }

    // Check if token is supported on both chains
    if (!isTokenSupportedOnChain(bridgeState.selectedToken, bridgeState.fromChain)) {
      return { isValid: false, error: `${bridgeState.selectedToken} not supported on ${bridgeState.fromChain}` };
    }

    if (!isTokenSupportedOnChain(bridgeState.selectedToken, bridgeState.toChain)) {
      return { isValid: false, error: `${bridgeState.selectedToken} not supported on ${bridgeState.toChain}` };
    }

    return { isValid: true };
  };

  const executeBridge = async (): Promise<void> => {
    const validation = validateBridge();
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const fromWallet = getWalletForChain(bridgeState.fromChain);

      if (!fromWallet.address) {
        throw new Error('Source wallet not connected');
      }

      const result = await wormholeService.initiateTransfer({
        fromChain: bridgeState.fromChain,
        toChain: bridgeState.toChain,
        tokenSymbol: bridgeState.selectedToken,
        amount: bridgeState.amount,
        recipientAddress: bridgeState.recipientAddress,
        senderAddress: fromWallet.address,
      });

      // Transaction completed successfully - no history needed

      // Update balances immediately to reflect the bridge
      const currentBalance = parseFloat(balances[bridgeState.fromChain]?.[bridgeState.selectedToken] || '0');
      const bridgeAmount = parseFloat(bridgeState.amount);
      const newBalance = Math.max(0, currentBalance - bridgeAmount).toString();

      // Update the balance in state
      const updatedBalances = {
        ...balances,
        [bridgeState.fromChain]: {
          ...balances[bridgeState.fromChain],
          [bridgeState.selectedToken]: newBalance
        }
      };

      setBalances(updatedBalances);

      // Save to localStorage
      try {
        localStorage.setItem('bridge-balances', JSON.stringify(updatedBalances));
      } catch (error) {
        console.error('Failed to save balances to localStorage:', error);
      }

      // Reset form
      setBridgeState(prev => ({
        ...prev,
        amount: '',
        fees: null,
        isLoading: false,
      }));

      console.log(`Bridge executed: ${bridgeState.amount} ${bridgeState.selectedToken} from ${bridgeState.fromChain} to ${bridgeState.toChain}`);
      console.log(`New balance: ${newBalance} ${bridgeState.selectedToken}`);

      return result;
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Bridge transaction failed',
        isLoading: false,
      }));
      throw error;
    }
  };

  const getTokenBalance = (chainId: string, tokenSymbol: string): string => {
    return balances[chainId]?.[tokenSymbol] || '0';
  };

  const getTokenPrice = (tokenSymbol: string): number => {
    return prices[tokenSymbol]?.price || 0;
  };

  const getUsdValue = (amount: string, tokenSymbol: string): number => {
    const price = getTokenPrice(tokenSymbol);
    return priceService.calculateUsdValue(amount, tokenSymbol, price);
  };

  const formatUsdValue = (value: number): string => {
    return priceService.formatUsdValue(value);
  };

  const getSupportedTokens = (chainId: string) => {
    return getSupportedTokensForChain(chainId);
  };

  const isTokenSupported = (tokenSymbol: string, chainId: string): boolean => {
    return isTokenSupportedOnChain(tokenSymbol, chainId);
  };

  return {
    // State
    bridgeState,
    balances,
    prices,
    pricesLoading,

    // Actions
    updateBridgeState,
    swapChains,
    setMaxAmount,
    executeBridge,
    loadBalances,

    // Utilities
    validateBridge,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getSupportedTokens,
    isTokenSupported,
  };
}
