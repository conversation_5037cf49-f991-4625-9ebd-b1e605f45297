import { useState } from 'react';

interface TokenLogoProps {
  src: string;
  alt: string;
  className?: string;
  fallbackText?: string;
}

export function TokenLogo({ src, alt, className = "w-6 h-6 rounded-full", fallbackText }: TokenLogoProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  if (imageError || !src) {
    // Fallback to a colored circle with initials
    const initials = fallbackText || alt.slice(0, 2).toUpperCase();
    const colors = [
      'bg-blue-500',
      'bg-green-500', 
      'bg-purple-500',
      'bg-orange-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-red-500',
      'bg-yellow-500',
    ];
    
    // Simple hash function to pick consistent color
    const colorIndex = alt.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    const bgColor = colors[colorIndex];
    
    return (
      <div className={`${className} ${bgColor} flex items-center justify-center text-white text-xs font-bold`}>
        {initials}
      </div>
    );
  }

  return (
    <div className="relative">
      <img
        src={src}
        alt={alt}
        className={`${className} ${!imageLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
      {!imageLoaded && (
        <div className={`${className} bg-gray-200 animate-pulse absolute inset-0`} />
      )}
    </div>
  );
}

interface ChainLogoProps {
  src: string;
  alt: string;
  className?: string;
}

export function ChainLogo({ src, alt, className = "w-6 h-6 rounded-full" }: ChainLogoProps) {
  return <TokenLogo src={src} alt={alt} className={className} fallbackText={alt.slice(0, 1)} />;
}
