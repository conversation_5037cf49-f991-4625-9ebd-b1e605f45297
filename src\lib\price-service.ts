// Price service for fetching real token prices and balances
import { Connection, PublicKey } from '@solana/web3.js';
import { getChainConfig, getTokenConfig } from './blockchain-config';

// CoinGecko API for price data
const COINGECKO_API = 'https://api.coingecko.com/api/v3';

// Token ID mappings for CoinGecko
const COINGECKO_IDS: Record<string, string> = {
  SOL: 'solana',
  ETH: 'ethereum',
  USDC: 'usd-coin',
  USDT: 'tether',
  DAI: 'dai',
  WBTC: 'wrapped-bitcoin',
  UNI: 'uniswap',
  AAVE: 'aave',
  LINK: 'chainlink',
  MATIC: 'matic-network',
  ARB: 'arbitrum',
  OP: 'optimism',
  SHIB: 'shiba-inu',
  PEPE: 'pepe',
  RAY: 'raydium',
  SRM: 'serum',
  ORCA: 'orca',
  BONK: 'bonk',
  JUP: 'jupiter-exchange-solana',
  PYTH: 'pyth-network',
  RENDER: 'render-token',
};

export interface TokenPrice {
  symbol: string;
  price: number;
  change24h: number;
}

export interface TokenBalance {
  symbol: string;
  balance: string;
  decimals: number;
  usdValue: number;
}

class PriceService {
  private priceCache: Map<string, { price: TokenPrice; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 60000; // 1 minute

  async getTokenPrices(symbols: string[]): Promise<Record<string, TokenPrice>> {
    const prices: Record<string, TokenPrice> = {};
    const symbolsToFetch: string[] = [];

    // Check cache first
    for (const symbol of symbols) {
      const cached = this.priceCache.get(symbol);
      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        prices[symbol] = cached.price;
      } else {
        symbolsToFetch.push(symbol);
      }
    }

    // Skip API fetch due to CORS issues, use fallback prices directly
    console.log('Skipping CoinGecko API due to CORS, using fallback prices for:', symbolsToFetch);

    // Fill in missing prices with fallback values
    const fallbackPrices: Record<string, number> = {
      SOL: 100,
      ETH: 2500,
      USDC: 1.0001,
      USDT: 1.0000,
      DAI: 1.0000,
      WBTC: 45000,
      UNI: 8.5,
      AAVE: 85,
      LINK: 12,
      MATIC: 0.85,
      ARB: 0.75,
      OP: 1.8,
    };

    for (const symbol of symbols) {
      if (!prices[symbol]) {
        prices[symbol] = {
          symbol,
          price: fallbackPrices[symbol] || 0,
          change24h: 0,
        };
        console.log(`Using fallback price for ${symbol}: $${fallbackPrices[symbol] || 0}`);
      }
    }

    console.log('Final prices:', prices);
    return prices;
  }

  async getSolanaTokenBalance(
    tokenMint: string,
    walletAddress: string,
    decimals: number
  ): Promise<string> {
    try {
      // Always use devnet for testing
      const connection = new Connection('https://api.devnet.solana.com', 'confirmed');

      console.log(`Fetching Solana balance for ${walletAddress}, token: ${tokenMint}`);

      const walletPublicKey = new PublicKey(walletAddress);

      if (tokenMint === 'So11111111111111111111111111111111111111112') {
        // SOL balance
        const balance = await connection.getBalance(walletPublicKey);
        const solBalance = (balance / Math.pow(10, decimals)).toString();
        console.log(`SOL balance: ${solBalance}`);
        return solBalance;
      } else {
        // SPL Token balance
        console.log(`Looking for SPL token accounts for mint: ${tokenMint}`);

        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
          walletPublicKey,
          { mint: new PublicKey(tokenMint) }
        );

        console.log(`Found ${tokenAccounts.value.length} token accounts`);

        if (tokenAccounts.value.length > 0) {
          const tokenAccount = tokenAccounts.value[0];
          const balance = tokenAccount.account.data.parsed.info.tokenAmount.uiAmount;
          console.log(`Token balance: ${balance}`);
          return balance?.toString() || '0';
        } else {
          console.log('No token accounts found');
        }
      }

      return '0';
    } catch (error) {
      console.error('Failed to fetch Solana token balance:', error);
      return '0';
    }
  }

  async getEvmTokenBalance(
    tokenAddress: string,
    walletAddress: string,
    chainId: string,
    decimals: number
  ): Promise<string> {
    try {
      const chainConfig = getChainConfig(chainId);
      if (!chainConfig) return '0';

      // For now, return mock data since we need proper RPC setup
      // In production, you would use ethers.js or web3.js to query the blockchain
      const mockBalances: Record<string, Record<string, string>> = {
        ethereum: { ETH: '2.5', USDC: '1000', USDT: '500' },
        base: { ETH: '0.75', USDC: '2000', USDT: '800' },
        arbitrum: { ETH: '1.2', USDC: '1500', USDT: '600' },
        polygon: { MATIC: '500', USDC: '2500', USDT: '1000' },
      };

      // This is a simplified mock - in reality you'd need to map addresses to symbols
      return mockBalances[chainId]?.['USDC'] || '0';
    } catch (error) {
      console.error('Failed to fetch EVM token balance:', error);
      return '0';
    }
  }

  async getTokenBalance(
    chainId: string,
    tokenSymbol: string,
    walletAddress: string
  ): Promise<TokenBalance> {
    console.log(`getTokenBalance: ${tokenSymbol} on ${chainId} for ${walletAddress}`);

    const tokenConfig = getTokenConfig(tokenSymbol);
    const chainConfig = getChainConfig(chainId);

    if (!tokenConfig || !chainConfig) {
      console.log(`getTokenBalance: Missing config for ${tokenSymbol} or ${chainId}`);
      return {
        symbol: tokenSymbol,
        balance: '0',
        decimals: 6,
        usdValue: 0,
      };
    }

    const tokenAddress = tokenConfig.addresses[chainId];
    if (!tokenAddress) {
      console.log(`getTokenBalance: No address for ${tokenSymbol} on ${chainId}`);
      return {
        symbol: tokenSymbol,
        balance: '0',
        decimals: tokenConfig.decimals,
        usdValue: 0,
      };
    }

    console.log(`getTokenBalance: Token address: ${tokenAddress}`);

    let balance = '0';

    try {
      if (chainConfig.type === 'solana') {
        console.log(`getTokenBalance: Fetching Solana balance...`);
        balance = await this.getSolanaTokenBalance(
          tokenAddress,
          walletAddress,
          tokenConfig.decimals
        );
      } else {
        console.log(`getTokenBalance: Fetching EVM balance...`);
        balance = await this.getEvmTokenBalance(
          tokenAddress,
          walletAddress,
          chainId,
          tokenConfig.decimals
        );
      }
    } catch (error) {
      console.error(`Failed to fetch balance for ${tokenSymbol} on ${chainId}:`, error);
    }

    console.log(`getTokenBalance: Final balance for ${tokenSymbol}: ${balance}`);

    // Use fallback prices instead of fetching
    const fallbackPrices: Record<string, number> = {
      SOL: 100,
      ETH: 2500,
      USDC: 1.0001,
      USDT: 1.0000,
      DAI: 1.0000,
      WBTC: 45000,
      UNI: 8.5,
      AAVE: 85,
      LINK: 12,
      MATIC: 0.85,
      ARB: 0.75,
      OP: 1.8,
    };

    const price = fallbackPrices[tokenSymbol] || 0;
    const balanceNum = parseFloat(balance);
    const usdValue = balanceNum * price;

    return {
      symbol: tokenSymbol,
      balance,
      decimals: tokenConfig.decimals,
      usdValue,
    };
  }

  calculateUsdValue(amount: string, tokenSymbol: string, price: number): number {
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum)) return 0;
    return amountNum * price;
  }

  formatUsdValue(value: number): string {
    if (value === 0) return '$0.00';
    if (value < 0.01) return '< $0.01';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }
}

export const priceService = new PriceService();

// We need to import React for the hooks
import React from 'react';

// Hook for using price data
export function useTokenPrices(symbols: string[] = []) {
  const [prices, setPrices] = React.useState<Record<string, TokenPrice>>({});
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    if (!symbols || symbols.length === 0) return;

    console.log('useTokenPrices: Fetching prices for symbols:', symbols);
    setLoading(true);
    priceService.getTokenPrices(symbols)
      .then((fetchedPrices) => {
        console.log('useTokenPrices: Received prices:', fetchedPrices);
        setPrices(fetchedPrices);
      })
      .catch((error) => {
        console.error('useTokenPrices: Error fetching prices:', error);
      })
      .finally(() => setLoading(false));
  }, [symbols ? symbols.join(',') : '']);

  return { prices, loading };
}
