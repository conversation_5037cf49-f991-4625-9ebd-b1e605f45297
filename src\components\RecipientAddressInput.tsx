import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useWallet } from "@/contexts/WalletContext";
import { getChainConfig } from "@/lib/blockchain-config";
import { formatAddress } from "@/lib/utils";
import { Wallet, Copy, Check } from "lucide-react";
import { toast } from "sonner";

interface RecipientAddressInputProps {
  chainId: string;
  onAddressChange: (address: string) => void;
}

export function RecipientAddressInput({ chainId, onAddressChange }: RecipientAddressInputProps) {
  const { getWalletForChain, isChainConnected } = useWallet();
  const [manualAddress, setManualAddress] = useState("");
  const [useConnectedWallet, setUseConnectedWallet] = useState(true);
  const [copied, setCopied] = useState(false);

  const chainConfig = getChainConfig(chainId);
  const wallet = getWalletForChain(chainId);
  const isConnected = isChainConnected(chainId);

  // Update parent component when address changes
  useEffect(() => {
    if (useConnectedWallet && isConnected && wallet.address) {
      onAddressChange(wallet.address);
    } else if (!useConnectedWallet && manualAddress) {
      onAddressChange(manualAddress);
    } else {
      onAddressChange("");
    }
  }, [useConnectedWallet, isConnected, wallet.address, manualAddress, onAddressChange]);

  const handleManualAddressChange = (value: string) => {
    setManualAddress(value);
  };

  const copyConnectedAddress = () => {
    if (wallet.address) {
      navigator.clipboard.writeText(wallet.address);
      setCopied(true);
      toast.success("Address copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const pasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setManualAddress(text);
      toast.success("Address pasted from clipboard");
    } catch (error) {
      toast.error("Failed to paste from clipboard");
    }
  };

  const validateAddress = (address: string): boolean => {
    if (!address) return false;
    
    if (chainConfig?.type === 'solana') {
      // Basic Solana address validation (base58, 32-44 chars)
      return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
    } else {
      // Basic EVM address validation (0x + 40 hex chars)
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
  };

  const currentAddress = useConnectedWallet && isConnected ? wallet.address : manualAddress;
  const isValidAddress = validateAddress(currentAddress || "");

  return (
    <div className="space-y-2">
      {/* Toggle between connected wallet and manual input */}
      <div className="flex gap-2">
        <Button
          variant={useConnectedWallet ? "default" : "outline"}
          size="sm"
          onClick={() => setUseConnectedWallet(true)}
          className="flex-1 h-8 text-xs"
          disabled={!isConnected}
        >
          <Wallet className="h-3 w-3 mr-1" />
          Connected Wallet
        </Button>
        <Button
          variant={!useConnectedWallet ? "default" : "outline"}
          size="sm"
          onClick={() => setUseConnectedWallet(false)}
          className="flex-1 h-8 text-xs"
        >
          Manual Address
        </Button>
      </div>

      {/* Address input/display */}
      {useConnectedWallet && isConnected ? (
        <div className="p-2 bg-secondary/30 rounded border border-border/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-mono">{formatAddress(wallet.address || "")}</span>
              <Badge variant="outline" className="border-success text-success text-xs">
                Connected
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={copyConnectedAddress}
              className="h-6 w-6 p-0"
            >
              {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-1">
          <div className="relative">
            <Input
              placeholder={`Enter ${chainConfig?.name} address...`}
              value={manualAddress}
              onChange={(e) => handleManualAddressChange(e.target.value)}
              className={`bg-secondary/50 border-border/50 pr-16 font-mono text-sm ${
                manualAddress && !isValidAddress ? 'border-destructive' : ''
              }`}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={pasteFromClipboard}
              className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs"
            >
              Paste
            </Button>
          </div>
          {manualAddress && !isValidAddress && (
            <p className="text-xs text-destructive">
              Invalid {chainConfig?.name} address format
            </p>
          )}
        </div>
      )}

      {/* Address validation status */}
      {currentAddress && (
        <div className="flex items-center gap-2">
          {isValidAddress ? (
            <Badge variant="outline" className="border-success text-success text-xs">
              ✓ Valid Address
            </Badge>
          ) : (
            <Badge variant="outline" className="border-destructive text-destructive text-xs">
              ✗ Invalid Address
            </Badge>
          )}
        </div>
      )}

      {/* Help text */}
      {!isConnected && useConnectedWallet && (
        <p className="text-xs text-muted-foreground">
          Connect your {chainConfig?.name} wallet or use manual address input
        </p>
      )}
    </div>
  );
}
