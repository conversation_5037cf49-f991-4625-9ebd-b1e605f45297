import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TokenLogo } from "@/components/TokenLogo";
import { Search, ChevronDown } from "lucide-react";
import { TokenConfig } from "@/lib/blockchain-config";
import { formatTokenAmount } from "@/lib/utils";

interface TokenSelectorProps {
  tokens: TokenConfig[];
  selectedToken: string;
  onTokenSelect: (tokenSymbol: string) => void;
  getTokenBalance: (tokenSymbol: string) => string;
  chainId: string;
  mode?: 'bridge' | 'native'; // bridge = cross-chain tokens, native = chain-native tokens only
}

export function TokenSelector({
  tokens,
  selectedToken,
  onTokenSelect,
  getTokenBalance,
  chainId,
  mode = 'bridge'
}: TokenSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const selectedTokenData = tokens.find(t => t.symbol === selectedToken);

  // Filter tokens based on search query
  const filteredTokens = tokens.filter(token =>
    token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
    token.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Categorize tokens
  const popularTokens = ['USDC', 'USDT', 'ETH', 'SOL', 'WBTC', 'DAI'];
  const defiTokens = ['UNI', 'AAVE', 'LINK', 'LDO', 'stETH', 'RAY', 'SRM', 'ORCA', 'CRV', 'COMP', 'MKR', 'SNX', 'SUSHI'];
  const layer2Tokens = ['ARB', 'OP', 'MATIC'];
  const memeTokens = ['SHIB', 'PEPE', 'DOGE', 'SAMO', 'BONK', 'WIF'];
  const solanaTokens = ['RAY', 'SRM', 'ORCA', 'MNGO', 'STEP', 'COPE', 'FIDA', 'SAMO', 'BONK', 'WIF', 'JUP', 'PYTH'];
  const allCategorizedTokens = [...popularTokens, ...defiTokens, ...layer2Tokens, ...memeTokens, ...solanaTokens];

  const categorizedTokens = {
    popular: filteredTokens.filter(token => popularTokens.includes(token.symbol)),
    defi: filteredTokens.filter(token => defiTokens.includes(token.symbol) && !popularTokens.includes(token.symbol)),
    solana: filteredTokens.filter(token => solanaTokens.includes(token.symbol) && !popularTokens.includes(token.symbol) && !defiTokens.includes(token.symbol)),
    layer2: filteredTokens.filter(token => layer2Tokens.includes(token.symbol)),
    meme: filteredTokens.filter(token => memeTokens.includes(token.symbol)),
    other: filteredTokens.filter(token => !allCategorizedTokens.includes(token.symbol)),
  };

  const handleTokenSelect = (tokenSymbol: string) => {
    onTokenSelect(tokenSymbol);
    setIsOpen(false);
    setSearchQuery("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full justify-between bg-secondary/50 border-border/50">
          <div className="flex items-center gap-2">
            {selectedTokenData?.logoUrl && (
              <TokenLogo
                src={selectedTokenData.logoUrl}
                alt={selectedTokenData.symbol}
                className="w-5 h-5 rounded-full"
                fallbackText={selectedTokenData.symbol}
              />
            )}
            <span className="font-medium">{selectedToken}</span>
            <span className="text-muted-foreground text-sm">{selectedTokenData?.name}</span>
          </div>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select Token</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tokens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Token List */}
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {/* Popular Tokens */}
              {categorizedTokens.popular.length > 0 && (
                <TokenCategory 
                  title="Popular" 
                  tokens={categorizedTokens.popular}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {/* DeFi Tokens */}
              {categorizedTokens.defi.length > 0 && (
                <TokenCategory
                  title="DeFi"
                  tokens={categorizedTokens.defi}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {/* Solana Ecosystem */}
              {categorizedTokens.solana.length > 0 && (
                <TokenCategory
                  title="Solana Ecosystem"
                  tokens={categorizedTokens.solana}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {/* Layer 2 Tokens */}
              {categorizedTokens.layer2.length > 0 && (
                <TokenCategory
                  title="Layer 2"
                  tokens={categorizedTokens.layer2}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {/* Meme Tokens */}
              {categorizedTokens.meme.length > 0 && (
                <TokenCategory 
                  title="Meme" 
                  tokens={categorizedTokens.meme}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {/* Other Tokens */}
              {categorizedTokens.other.length > 0 && (
                <TokenCategory 
                  title="Other" 
                  tokens={categorizedTokens.other}
                  onTokenSelect={handleTokenSelect}
                  getTokenBalance={getTokenBalance}
                  selectedToken={selectedToken}
                />
              )}

              {filteredTokens.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  No tokens found
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface TokenCategoryProps {
  title: string;
  tokens: TokenConfig[];
  onTokenSelect: (tokenSymbol: string) => void;
  getTokenBalance: (tokenSymbol: string) => string;
  selectedToken: string;
}

function TokenCategory({ title, tokens, onTokenSelect, getTokenBalance, selectedToken }: TokenCategoryProps) {
  return (
    <div>
      <h3 className="text-sm font-medium text-muted-foreground mb-2">{title}</h3>
      <div className="space-y-1">
        {tokens.map((token) => {
          const balance = getTokenBalance(token.symbol);
          const isSelected = token.symbol === selectedToken;
          
          return (
            <div
              key={token.symbol}
              className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                isSelected 
                  ? 'bg-primary/10 border border-primary/20' 
                  : 'hover:bg-secondary/50'
              }`}
              onClick={() => onTokenSelect(token.symbol)}
            >
              <div className="flex items-center gap-3">
                <TokenLogo
                  src={token.logoUrl || ''}
                  alt={token.symbol}
                  className="w-8 h-8 rounded-full"
                  fallbackText={token.symbol}
                />
                <div>
                  <div className="font-medium">{token.symbol}</div>
                  <div className="text-sm text-muted-foreground">{token.name}</div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-medium">{formatTokenAmount(balance)}</div>
                {isSelected && (
                  <Badge variant="outline" className="border-primary text-primary text-xs">
                    Selected
                  </Badge>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
