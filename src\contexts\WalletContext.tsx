import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Connection, PublicKey } from '@solana/web3.js';
import { getChainConfig, getEvmChainIdHex, SUPPORTED_CHAINS } from '@/lib/blockchain-config';

// Wallet connection state
export interface WalletState {
  isConnected: boolean;
  address: string | null;
  chainId: string | null;
  balance: string | null;
  connecting: boolean;
  error: string | null;
}

// Wallet context interface
export interface WalletContextType {
  // Solana wallet state
  solanaWallet: WalletState;
  // EVM wallet state  
  evmWallet: WalletState;
  
  // Connection methods
  connectSolanaWallet: () => Promise<void>;
  connectEvmWallet: (chainId: string) => Promise<void>;
  disconnectSolanaWallet: () => void;
  disconnectEvmWallet: () => void;
  
  // Utility methods
  getWalletForChain: (chainId: string) => WalletState;
  isChainConnected: (chainId: string) => boolean;
  switchEvmChain: (chainId: string) => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

// Initial wallet state
const initialWalletState: WalletState = {
  isConnected: false,
  address: null,
  chainId: null,
  balance: null,
  connecting: false,
  error: null,
};

export function WalletProvider({ children }: { children: ReactNode }) {
  const [solanaWallet, setSolanaWallet] = useState<WalletState>(initialWalletState);
  const [evmWallet, setEvmWallet] = useState<WalletState>(initialWalletState);

  // Check for existing connections on mount
  useEffect(() => {
    checkExistingConnections();
  }, []);

  const checkExistingConnections = async () => {
    // Check for existing Solana connection
    if (typeof window !== 'undefined' && window.solana?.isPhantom) {
      try {
        const response = await window.solana.connect({ onlyIfTrusted: true });
        if (response.publicKey) {
          setSolanaWallet({
            isConnected: true,
            address: response.publicKey.toString(),
            chainId: 'solana',
            balance: null,
            connecting: false,
            error: null,
          });
        }
      } catch (error) {
        console.log('No existing Solana connection found');
      }
    }

    // Check for existing EVM connection
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
        if (accounts.length > 0) {
          const chainId = await window.ethereum.request({ method: 'eth_chainId' });
          setEvmWallet({
            isConnected: true,
            address: accounts[0],
            chainId: getChainIdFromEvmChainId(chainId),
            balance: null,
            connecting: false,
            error: null,
          });
        }
      } catch (error) {
        console.log('No existing EVM connection found');
      }
    }
  };

  const connectSolanaWallet = async () => {
    if (!window.solana?.isPhantom) {
      setSolanaWallet(prev => ({ ...prev, error: 'Phantom wallet not found. Please install Phantom.' }));
      return;
    }

    setSolanaWallet(prev => ({ ...prev, connecting: true, error: null }));

    try {
      const response = await window.solana.connect();
      setSolanaWallet({
        isConnected: true,
        address: response.publicKey.toString(),
        chainId: 'solana',
        balance: null,
        connecting: false,
        error: null,
      });
    } catch (error) {
      setSolanaWallet(prev => ({
        ...prev,
        connecting: false,
        error: 'Failed to connect to Phantom wallet',
      }));
    }
  };

  const connectEvmWallet = async (chainId: string) => {
    if (!window.ethereum) {
      setEvmWallet(prev => ({ ...prev, error: 'MetaMask not found. Please install MetaMask.' }));
      return;
    }

    setEvmWallet(prev => ({ ...prev, connecting: true, error: null }));

    try {
      // Request account access
      const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      if (accounts.length === 0) {
        throw new Error('No accounts found');
      }

      // Switch to the correct chain
      await switchEvmChain(chainId);

      setEvmWallet({
        isConnected: true,
        address: accounts[0],
        chainId,
        balance: null,
        connecting: false,
        error: null,
      });
    } catch (error) {
      setEvmWallet(prev => ({
        ...prev,
        connecting: false,
        error: 'Failed to connect to MetaMask',
      }));
    }
  };

  const switchEvmChain = async (chainId: string) => {
    if (!window.ethereum) return;

    const chainConfig = getChainConfig(chainId);
    if (!chainConfig || chainConfig.type !== 'evm') return;

    const evmChainId = getEvmChainIdHex(chainId);

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: evmChainId }],
      });
    } catch (switchError: any) {
      // Chain not added to wallet
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: evmChainId,
              chainName: chainConfig.name,
              rpcUrls: [chainConfig.rpcUrl],
              nativeCurrency: chainConfig.nativeCurrency,
              blockExplorerUrls: [chainConfig.explorerUrl],
            }],
          });
        } catch (addError) {
          throw new Error('Failed to add chain to wallet');
        }
      } else {
        throw switchError;
      }
    }
  };

  const disconnectSolanaWallet = () => {
    if (window.solana?.isPhantom) {
      window.solana.disconnect();
    }
    setSolanaWallet(initialWalletState);
  };

  const disconnectEvmWallet = () => {
    setEvmWallet(initialWalletState);
  };

  const getWalletForChain = (chainId: string): WalletState => {
    const chainConfig = getChainConfig(chainId);
    if (!chainConfig) return initialWalletState;
    
    return chainConfig.type === 'solana' ? solanaWallet : evmWallet;
  };

  const isChainConnected = (chainId: string): boolean => {
    const wallet = getWalletForChain(chainId);
    return wallet.isConnected && wallet.chainId === chainId;
  };

  const value: WalletContextType = {
    solanaWallet,
    evmWallet,
    connectSolanaWallet,
    connectEvmWallet,
    disconnectSolanaWallet,
    disconnectEvmWallet,
    getWalletForChain,
    isChainConnected,
    switchEvmChain,
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
}

export function useWallet() {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}

// Helper functions
function getChainIdFromEvmChainId(evmChainId: string): string {
  const chainIdNum = parseInt(evmChainId, 16);
  
  // Map EVM chain IDs to our internal chain IDs
  switch (chainIdNum) {
    case 8453: // Base mainnet
    case 84532: // Base testnet
      return 'base';
    case 42161: // Arbitrum mainnet  
    case 421614: // Arbitrum testnet
      return 'arbitrum';
    default:
      return 'unknown';
  }
}



// Extend window interface for wallet types
declare global {
  interface Window {
    solana?: {
      isPhantom?: boolean;
      connect: (options?: { onlyIfTrusted?: boolean }) => Promise<{ publicKey: PublicKey }>;
      disconnect: () => void;
    };
    ethereum?: {
      isMetaMask?: boolean;
      request: (args: { method: string; params?: any[] }) => Promise<any>;
    };
  }
}
