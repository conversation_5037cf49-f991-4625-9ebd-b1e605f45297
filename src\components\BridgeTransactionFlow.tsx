import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ChainLogo, TokenLogo } from "@/components/TokenLogo";
import { getChainConfig, getTokenConfig } from "@/lib/blockchain-config";
import { formatTokenAmount, formatUSD } from "@/lib/utils";
import { CheckCircle, Clock, AlertCircle, ExternalLink, Loader2, ArrowUpDown } from "lucide-react";

export interface BridgeStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  txHash?: string;
  explorerUrl?: string;
}

export interface BridgeTransactionData {
  fromChain: string;
  toChain: string;
  token: string;
  amount: string;
  recipientAddress: string;
  estimatedFees: {
    bridgeFee: string;
    networkFee: string;
    totalFee: string;
  };
  estimatedTime: number;
}

interface BridgeTransactionFlowProps {
  isOpen: boolean;
  onClose: () => void;
  transactionData: BridgeTransactionData | null;
  onConfirm: () => Promise<void>;
}

export function BridgeTransactionFlow({ 
  isOpen, 
  onClose, 
  transactionData, 
  onConfirm 
}: BridgeTransactionFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<BridgeStep[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (transactionData) {
      const initialSteps: BridgeStep[] = [
        {
          id: 'wallet',
          title: 'Wallet Signature',
          description: `Sign transaction with your wallet`,
          status: 'pending'
        },
        {
          id: 'initiate',
          title: 'Lock Tokens',
          description: `Lock ${transactionData.token} on ${transactionData.fromChain}`,
          status: 'pending'
        },
        {
          id: 'attestation',
          title: 'Wormhole Attestation',
          description: 'Waiting for cross-chain proof',
          status: 'pending'
        },
        {
          id: 'complete',
          title: 'Mint Tokens',
          description: `Mint tokens on ${transactionData.toChain}`,
          status: 'pending'
        }
      ];
      setSteps(initialSteps);
      setCurrentStep(0);
      setError(null);
    }
  }, [transactionData]);

  const handleConfirm = async () => {
    if (!transactionData) return;

    setIsProcessing(true);
    setError(null);

    try {
      // Step 1: Wallet Signature
      setSteps(prev => prev.map((step, index) =>
        index === 0 ? { ...step, status: 'processing' } : step
      ));
      setCurrentStep(0);

      console.log('🖊️ Requesting wallet signature...');

      // Step 2: Initiate Bridge (this calls the real bridge function)
      setSteps(prev => prev.map((step, index) =>
        index === 1 ? { ...step, status: 'processing' } : step
      ));
      setCurrentStep(1);

      console.log('🌉 Calling real bridge function...');
      const result = await onConfirm();

      // Mark wallet signature as completed
      setSteps(prev => prev.map((step, index) =>
        index === 0 ? {
          ...step,
          status: 'completed',
          txHash: 'wallet_signed',
          explorerUrl: undefined
        } : step
      ));

      // Mark initiation as completed
      setSteps(prev => prev.map((step, index) =>
        index === 1 ? {
          ...step,
          status: 'completed',
          txHash: result?.sourceTxId || 'bridge_initiated',
          explorerUrl: result?.sourceTxId ? `https://explorer.solana.com/tx/${result.sourceTxId}?cluster=devnet` : undefined
        } : step
      ));

      // Step 3: Wormhole Attestation
      setSteps(prev => prev.map((step, index) =>
        index === 2 ? { ...step, status: 'processing' } : step
      ));
      setCurrentStep(2);

      // Simulate attestation wait (in real implementation, this would be handled by the bridge service)
      await new Promise(resolve => setTimeout(resolve, 3000));

      setSteps(prev => prev.map((step, index) =>
        index === 2 ? {
          ...step,
          status: 'completed',
          txHash: result?.attestation || 'attestation_received'
        } : step
      ));

      // Step 4: Complete Bridge
      setSteps(prev => prev.map((step, index) =>
        index === 3 ? { ...step, status: 'processing' } : step
      ));
      setCurrentStep(3);

      // Simulate completion (in real implementation, this would be automatic)
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSteps(prev => prev.map((step, index) =>
        index === 3 ? {
          ...step,
          status: 'completed',
          txHash: result?.destTxId || 'bridge_completed',
          explorerUrl: result?.destTxId ? `https://basescan.org/tx/${result.destTxId}` : undefined
        } : step
      ));

      setCurrentStep(4); // All steps completed

    } catch (error) {
      console.error('Bridge transaction failed:', error);
      setError(error instanceof Error ? error.message : 'Bridge transaction failed');
      
      setSteps(prev => prev.map((step, index) => 
        index === currentStep ? { ...step, status: 'failed' } : step
      ));
    } finally {
      setIsProcessing(false);
    }
  };

  const getStepIcon = (step: BridgeStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStepColor = (step: BridgeStep) => {
    switch (step.status) {
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'processing':
        return 'border-blue-500 bg-blue-50';
      case 'failed':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const progress = steps.length > 0 ? (steps.filter(s => s.status === 'completed').length / steps.length) * 100 : 0;
  const isCompleted = currentStep >= steps.length;

  if (!transactionData) return null;

  const fromChain = getChainConfig(transactionData.fromChain);
  const toChain = getChainConfig(transactionData.toChain);
  const token = getTokenConfig(transactionData.token);

  // Render different cards based on current step
  if (currentStep === 0 && !isProcessing) {
    // Confirmation Card
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <ConfirmationCard
            transactionData={transactionData}
            onConfirm={handleConfirm}
            onCancel={onClose}
          />
        </DialogContent>
      </Dialog>
    );
  }

  if (isCompleted && !error) {
    // Success Card
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <SuccessCard
            transactionData={transactionData}
            onClose={onClose}
          />
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    // Error Card
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <ErrorCard
            error={error}
            onRetry={handleConfirm}
            onClose={onClose}
          />
        </DialogContent>
      </Dialog>
    );
  }

  // Processing Card
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <ProcessingCard
          steps={steps}
          currentStep={currentStep}
          progress={progress}
        />
      </DialogContent>
    </Dialog>
  );
}
// Confirmation Card Component
function ConfirmationCard({ transactionData, onConfirm, onCancel }: {
  transactionData: BridgeTransactionData | null;
  onConfirm: () => void;
  onCancel: () => void;
}) {
  if (!transactionData) return null;

  const fromChain = getChainConfig(transactionData.fromChain);
  const toChain = getChainConfig(transactionData.toChain);
  const token = getTokenConfig(transactionData.token);

  return (
    <>
      <DialogHeader>
        <DialogTitle>Confirm Bridge Transaction</DialogTitle>
      </DialogHeader>
      <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <ArrowUpDown className="h-8 w-8 text-blue-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Confirm Bridge Transaction</h2>
        <p className="text-gray-600 mt-2">Review your transaction details before proceeding</p>
      </div>

      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <ChainLogo src={fromChain?.logo || ''} alt={fromChain?.name || ''} className="w-8 h-8" />
            <div>
              <div className="font-medium text-gray-900">{fromChain?.name}</div>
              <div className="text-sm text-gray-500">From</div>
            </div>
          </div>
          <div className="text-gray-400">
            <ArrowUpDown className="h-5 w-5" />
          </div>
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="font-medium text-gray-900">{toChain?.name}</div>
              <div className="text-sm text-gray-500">To</div>
            </div>
            <ChainLogo src={toChain?.logo || ''} alt={toChain?.name || ''} className="w-8 h-8" />
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-600">Amount</span>
            <div className="flex items-center gap-2">
              <TokenLogo src={token?.logoUrl || ''} alt={token?.symbol || ''} className="w-5 h-5" />
              <span className="font-medium text-gray-900">{transactionData.amount} {transactionData.token}</span>
            </div>
          </div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-600">Estimated Fee</span>
            <span className="font-medium text-gray-900">{transactionData.estimatedFees.totalFee}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Estimated Time</span>
            <span className="font-medium text-gray-900">~{transactionData.estimatedTime} minutes</span>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
        <Button onClick={onConfirm} className="flex-1 bg-blue-600 hover:bg-blue-700">
          Confirm Bridge
        </Button>
      </div>
    </div>
    </>
  );
}

// Processing Card Component
function ProcessingCard({ steps, currentStep, progress }: {
  steps: BridgeStep[];
  currentStep: number;
  progress: number;
}) {
  return (
    <>
      <DialogHeader>
        <DialogTitle>Processing Bridge</DialogTitle>
      </DialogHeader>
      <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Processing Bridge</h2>
        <p className="text-gray-600 mt-2">Please wait while we process your transaction</p>
      </div>

      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {step.status === 'completed' && <CheckCircle className="h-5 w-5 text-green-500" />}
              {step.status === 'processing' && <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />}
              {step.status === 'pending' && <Clock className="h-5 w-5 text-gray-400" />}
              {step.status === 'failed' && <AlertCircle className="h-5 w-5 text-red-500" />}
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">{step.title}</div>
              <div className="text-sm text-gray-600">{step.description}</div>
            </div>
            {step.txHash && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(step.explorerUrl, '_blank')}
                className="h-8 w-8 p-0"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}
      </div>
    </div>
    </>
  );
}

// Success Card Component
function SuccessCard({ transactionData, onClose }: {
  transactionData: BridgeTransactionData | null;
  onClose: () => void;
}) {
  if (!transactionData) return null;

  const fromChain = getChainConfig(transactionData.fromChain);
  const toChain = getChainConfig(transactionData.toChain);
  const token = getTokenConfig(transactionData.token);

  return (
    <>
      <DialogHeader>
        <DialogTitle>Bridge Successful!</DialogTitle>
      </DialogHeader>
      <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Bridge Successful!</h2>
        <p className="text-gray-600 mt-2">Your tokens have been successfully bridged</p>
      </div>

      <div className="bg-green-50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <ChainLogo src={fromChain?.logo || ''} alt={fromChain?.name || ''} className="w-8 h-8" />
            <div>
              <div className="font-medium text-gray-900">{fromChain?.name}</div>
              <div className="text-sm text-gray-500">From</div>
            </div>
          </div>
          <div className="text-green-600">
            <CheckCircle className="h-5 w-5" />
          </div>
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="font-medium text-gray-900">{toChain?.name}</div>
              <div className="text-sm text-gray-500">To</div>
            </div>
            <ChainLogo src={toChain?.logo || ''} alt={toChain?.name || ''} className="w-8 h-8" />
          </div>
        </div>

        <div className="border-t border-green-200 pt-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            <TokenLogo src={token?.logoUrl || ''} alt={token?.symbol || ''} className="w-6 h-6" />
            <span className="text-lg font-semibold text-gray-900">{transactionData.amount} {transactionData.token}</span>
          </div>
          <div className="text-center text-sm text-gray-600">
            Successfully bridged to {toChain?.name}
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" className="flex-1">
          View Transaction
        </Button>
        <Button onClick={onClose} className="flex-1 bg-green-600 hover:bg-green-700">
          Done
        </Button>
      </div>
    </div>
    </>
  );
}

// Error Card Component
function ErrorCard({ error, onRetry, onClose }: {
  error: string;
  onRetry: () => void;
  onClose: () => void;
}) {
  return (
    <>
      <DialogHeader>
        <DialogTitle>Transaction Failed</DialogTitle>
      </DialogHeader>
      <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertCircle className="h-8 w-8 text-red-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Transaction Failed</h2>
        <p className="text-gray-600 mt-2">Something went wrong with your bridge transaction</p>
      </div>

      <div className="bg-red-50 rounded-lg p-4 mb-6">
        <div className="text-sm text-red-800">{error}</div>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={onClose} className="flex-1">
          Close
        </Button>
        <Button onClick={onRetry} className="flex-1 bg-red-600 hover:bg-red-700">
          Try Again
        </Button>
      </div>
    </div>
    </>
  );
}
