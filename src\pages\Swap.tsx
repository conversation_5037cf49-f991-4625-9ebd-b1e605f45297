import { Header } from "@/components/Header";
import { SwapInterface } from "@/components/SwapInterface";
import { NetworkStatus } from "@/components/NetworkStatus";

const Swap = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20">
      <Header />
      
      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="w-full max-w-lg">
            <SwapInterface />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Swap;
