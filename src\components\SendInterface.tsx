import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { WalletConnection } from "@/components/WalletConnection";
import { TokenSelector } from "@/components/TokenSelector";
import { TokenLogo } from "@/components/TokenLogo";
import { useWallet } from "@/contexts/WalletContext";
import { useSend } from "@/hooks/useSend";
import { formatTokenAmount } from "@/lib/utils";
import { Plus, Trash2, Send, Users, Zap } from "lucide-react";

export const SendInterface = () => {
  const { isChainConnected } = useWallet();
  const {
    sendState,
    updateSendState,
    addRecipient,
    removeRecipient,
    updateRecipient,
    executeSend,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getSupportedTokens,
    validateSend,
    getTotalAmount,
    getEstimatedFee,
  } = useSend();

  const supportedTokens = getSupportedTokens();
  const tokenBalance = getTokenBalance('solana', sendState.selectedToken);
  const tokenPrice = getTokenPrice(sendState.selectedToken);
  const totalAmount = getTotalAmount();
  const totalUsdValue = getUsdValue(totalAmount, sendState.selectedToken);
  const estimatedFee = getEstimatedFee();
  const validation = validateSend();

  const setMaxAmount = () => {
    if (sendState.recipients.length === 1) {
      updateRecipient(0, { amount: tokenBalance });
    }
  };

  const handleSend = async () => {
    if (!validation.isValid) return;
    
    try {
      await executeSend();
    } catch (error) {
      console.error('Send failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">Multi-Send</h1>
        <p className="text-muted-foreground">
          Send tokens to multiple wallets with a single transaction
        </p>
      </div>

      <Card className="p-6 bg-card/50 backdrop-blur-sm border-border/50">
        <div className="space-y-6">
          {/* Token Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Token</label>
              <div className="text-right">
                <div className="text-xs text-muted-foreground">
                  Balance: {formatTokenAmount(tokenBalance)} {sendState.selectedToken}
                </div>
                {tokenBalance !== '0' && tokenPrice > 0 && (
                  <div className="text-xs text-muted-foreground">
                    ≈ {formatUsdValue(getUsdValue(tokenBalance, sendState.selectedToken))}
                  </div>
                )}
              </div>
            </div>

            <TokenSelector
              tokens={supportedTokens}
              selectedToken={sendState.selectedToken}
              onTokenSelect={(token) => updateSendState({ selectedToken: token })}
              getTokenBalance={(token) => getTokenBalance('solana', token)}
              chainId="solana"
              mode="native"
            />
          </div>

          {/* Recipients */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Recipients</label>
              <Badge variant="outline" className="text-xs">
                <Users className="h-3 w-3 mr-1" />
                {sendState.recipients.length} recipient{sendState.recipients.length !== 1 ? 's' : ''}
              </Badge>
            </div>

            <div className="space-y-3">
              {sendState.recipients.map((recipient, index) => (
                <div key={index} className="p-3 bg-secondary/30 rounded-lg border border-border/50">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs font-medium text-muted-foreground">#{index + 1}</span>
                    {sendState.recipients.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeRecipient(index)}
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive/80"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Input
                      placeholder="Recipient wallet address"
                      value={recipient.address}
                      onChange={(e) => updateRecipient(index, { address: e.target.value })}
                      className="bg-background/50 border-border/50 text-sm"
                    />
                    
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="0.00"
                        value={recipient.amount}
                        onChange={(e) => updateRecipient(index, { amount: e.target.value })}
                        className="bg-background/50 border-border/50 pr-16"
                      />
                      {index === 0 && sendState.recipients.length === 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={setMaxAmount}
                          className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs text-primary hover:text-primary/80"
                        >
                          MAX
                        </Button>
                      )}
                    </div>

                    {recipient.amount && (
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>≈ {formatUsdValue(getUsdValue(recipient.amount, sendState.selectedToken))}</span>
                        <div className="flex items-center gap-1">
                          <TokenLogo 
                            src={supportedTokens.find(t => t.symbol === sendState.selectedToken)?.logoUrl || ''} 
                            alt={sendState.selectedToken}
                            className="w-3 h-3"
                            fallbackText={sendState.selectedToken}
                          />
                          <span>{recipient.amount} {sendState.selectedToken}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={addRecipient}
              className="w-full border-dashed border-primary/30 text-primary hover:border-primary/50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Recipient
            </Button>
          </div>

          {/* Summary */}
          {totalAmount !== '0' && (
            <div className="p-3 bg-secondary/30 rounded-lg border border-border/50">
              <h3 className="text-sm font-medium mb-2 text-foreground">Transaction Summary</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Amount:</span>
                  <span className="text-foreground font-medium">
                    {formatTokenAmount(totalAmount)} {sendState.selectedToken}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">USD Value:</span>
                  <span className="text-foreground">{formatUsdValue(totalUsdValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network Fee:</span>
                  <span className="text-foreground">{estimatedFee} SOL</span>
                </div>
                <div className="flex justify-between border-t border-border/50 pt-1">
                  <span className="text-muted-foreground">Recipients:</span>
                  <span className="text-foreground font-medium">{sendState.recipients.length}</span>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {!validation.isValid && validation.error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-sm text-destructive">{validation.error}</p>
            </div>
          )}

          {/* Wallet Connection */}
          {!isChainConnected('solana') && (
            <div className="text-center">
              <WalletConnection chainId="solana" className="w-full" />
            </div>
          )}

          {/* Send Button */}
          <Button
            onClick={handleSend}
            disabled={!validation.isValid || sendState.isLoading}
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            size="lg"
          >
            {sendState.isLoading ? (
              <>
                <Zap className="h-4 w-4 mr-2 animate-pulse" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {!validation.isValid ? 'Enter Details' : `Send to ${sendState.recipients.length} Recipient${sendState.recipients.length !== 1 ? 's' : ''}`}
              </>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
};
