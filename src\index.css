@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 26% 4%;
    --foreground: 210 40% 98%;

    --card: 220 23% 6%;
    --card-foreground: 210 40% 98%;

    --popover: 220 23% 6%;
    --popover-foreground: 210 40% 98%;

    --primary: 189 100% 56%;
    --primary-foreground: 220 26% 4%;

    --secondary: 220 17% 12%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 17% 12%;
    --muted-foreground: 215 20% 65%;

    --accent: 189 84% 42%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 17% 15%;
    --input: 220 17% 15%;
    --ring: 189 100% 56%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 220 26% 4%;

    /* Bridge-specific design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(189 100% 56%), hsl(189 84% 42%));
    --gradient-background: linear-gradient(180deg, hsl(220 26% 4%), hsl(220 23% 6%));
    --gradient-card: linear-gradient(145deg, hsl(220 23% 6%), hsl(220 17% 12%));
    
    --shadow-glow: 0 0 40px hsl(189 100% 56% / 0.3);
    --shadow-elevated: 0 10px 30px -10px hsl(220 26% 4% / 0.8);
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}