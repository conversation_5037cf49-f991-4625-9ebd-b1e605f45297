import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { WalletConnection } from "@/components/WalletConnection";
import { TokenSelector } from "@/components/TokenSelector";
import { RecipientAddressInput } from "@/components/RecipientAddressInput";
import { BridgeTransactionFlow, BridgeTransactionData } from "@/components/BridgeTransactionFlow";
import { ChainLogo, TokenLogo } from "@/components/TokenLogo";
import { useWallet } from "@/contexts/WalletContext";
import { useBridge } from "@/hooks/useBridge";
import { wormholeService } from "@/lib/wormhole-service";
import { SUPPORTED_CHAINS, getSupportedTokensF<PERSON><PERSON><PERSON><PERSON>, getNativeTokens<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/blockchain-config";
import { formatTokenAmount, formatUSD } from "@/lib/utils";
import { ArrowUpDown, Shield, Clock, Loader2 } from "lucide-react";
import { toast } from "sonner";

export const BridgeInterface = () => {
  const { isChainConnected } = useWallet();
  const [showTransactionFlow, setShowTransactionFlow] = useState(false);
  const [transactionData, setTransactionData] = useState<BridgeTransactionData | null>(null);
  const [estimatedFees, setEstimatedFees] = useState<{
    bridgeFee: string;
    networkFee: string;
    totalFee: string;
  } | null>(null);
  const {
    bridgeState,
    updateBridgeState,
    swapChains,
    setMaxAmount,
    executeBridge,
    validateBridge,
    getTokenBalance,
    getTokenPrice,
    getUsdValue,
    formatUsdValue,
    getSupportedTokens,
    loadBalances,
    prices,
    pricesLoading,
    balances,
  } = useBridge();

  const fromChainConfig = SUPPORTED_CHAINS[bridgeState.fromChain];
  const toChainConfig = SUPPORTED_CHAINS[bridgeState.toChain];
  const supportedTokens = getSupportedTokens(bridgeState.fromChain);
  const selectedTokenBalance = getTokenBalance(bridgeState.fromChain, bridgeState.selectedToken);
  const selectedTokenPrice = getTokenPrice(bridgeState.selectedToken);
  const amountUsdValue = getUsdValue(bridgeState.amount, bridgeState.selectedToken);
  const validation = validateBridge();
  const canBridge = validation.isValid && !bridgeState.isLoading;

  // Force load balances when component mounts or wallet connects (with debouncing)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isChainConnected(bridgeState.fromChain)) {
        console.log('BridgeInterface: Wallet connected, loading balances...');
        loadBalances();
      }
    }, 1000); // 1 second delay to avoid rapid calls

    return () => clearTimeout(timeoutId);
  }, [isChainConnected, bridgeState.fromChain]);

  // Calculate fees when amount or chains change
  useEffect(() => {
    const calculateFees = async () => {
      if (bridgeState.amount && parseFloat(bridgeState.amount) > 0) {
        try {
          const fees = await wormholeService.calculateFees(
            bridgeState.fromChain,
            bridgeState.toChain,
            bridgeState.selectedToken,
            bridgeState.amount
          );
          setEstimatedFees(fees);
        } catch (error) {
          console.error('Failed to calculate fees:', error);
          setEstimatedFees(null);
        }
      } else {
        setEstimatedFees(null);
      }
    };

    calculateFees();
  }, [bridgeState.amount, bridgeState.fromChain, bridgeState.toChain, bridgeState.selectedToken]);

  const handleBridge = async () => {
    if (!estimatedFees) {
      toast.error('Please wait for fee calculation');
      return;
    }

    // Prepare transaction data
    const txData: BridgeTransactionData = {
      fromChain: bridgeState.fromChain,
      toChain: bridgeState.toChain,
      token: bridgeState.selectedToken,
      amount: bridgeState.amount,
      recipientAddress: bridgeState.recipientAddress,
      estimatedFees,
      estimatedTime: 5, // 5 minutes
    };

    setTransactionData(txData);
    setShowTransactionFlow(true);
  };

  const handleConfirmBridge = async () => {
    try {
      const result = await executeBridge();
      // Success will be shown in the success card
      return result;
    } catch (error) {
      // Error will be shown in the error card
      throw error;
    }
  };

  return (
    <div className="w-full max-w-lg mx-auto space-y-4">
      {/* Bridge Card */}
      <Card className="p-4 bg-gradient-card border-border/50 shadow-elevated">
        <div className="space-y-4">
          {/* Header */}
          <div className="text-center">
            <h2 className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Cross-Chain Bridge
            </h2>
            <p className="text-muted-foreground text-xs mt-1">
              Secure bridging with Token-2022 support
            </p>
          </div>

          {/* From Section */}
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">From</label>

              {/* From Network */}
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Network</label>
                <Select
                  value={bridgeState.fromChain}
                  onValueChange={(value) => updateBridgeState({ fromChain: value })}
                >
                  <SelectTrigger className="bg-secondary/50 border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(SUPPORTED_CHAINS).map((chain) => (
                      <SelectItem key={chain.id} value={chain.id}>
                        <div className="flex items-center gap-2">
                          <ChainLogo
                            src={chain.logo}
                            alt={chain.name}
                            className="w-5 h-5 rounded-full"
                          />
                          <span>{chain.name}</span>
                          {isChainConnected(chain.id) && (
                            <Badge variant="outline" className="border-success text-success text-xs ml-auto">
                              Connected
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* From Token */}
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Token</label>
                <TokenSelector
                  tokens={supportedTokens}
                  selectedToken={bridgeState.selectedToken}
                  onTokenSelect={(tokenSymbol) => updateBridgeState({ selectedToken: tokenSymbol })}
                  getTokenBalance={getTokenBalance}
                  chainId={bridgeState.fromChain}
                />
              </div>

              {/* Wallet Connection */}
              {!isChainConnected(bridgeState.fromChain) && (
                <div className="mt-2">
                  <WalletConnection chainId={bridgeState.fromChain} className="w-full h-8 text-xs" />
                </div>
              )}
            </div>

            {/* Swap Button */}
            <div className="flex justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={swapChains}
                className="rounded-full border-primary/20 hover:border-primary/40 transition-all duration-300"
                disabled={bridgeState.isLoading}
              >
                <ArrowUpDown className="h-4 w-4" />
              </Button>
            </div>

            {/* To Section */}
            <div className="space-y-2">
              <label className="text-sm font-medium">To</label>

              {/* To Network */}
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Network</label>
                <Select
                  value={bridgeState.toChain}
                  onValueChange={(value) => updateBridgeState({ toChain: value })}
                >
                  <SelectTrigger className="bg-secondary/50 border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(SUPPORTED_CHAINS).map((chain) => (
                      <SelectItem key={chain.id} value={chain.id}>
                        <div className="flex items-center gap-2">
                          <ChainLogo
                            src={chain.logo}
                            alt={chain.name}
                            className="w-5 h-5 rounded-full"
                          />
                          <span>{chain.name}</span>
                          {isChainConnected(chain.id) && (
                            <Badge variant="outline" className="border-success text-success text-xs ml-auto">
                              Connected
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* To Token (auto-selected based on from token) */}
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">You'll receive</label>
                <div className="p-2 bg-secondary/30 rounded border border-border/50">
                  <div className="flex items-center gap-2">
                    <TokenLogo
                      src={supportedTokens.find(t => t.symbol === bridgeState.selectedToken)?.logoUrl || ''}
                      alt={bridgeState.selectedToken}
                      className="w-5 h-5 rounded-full"
                      fallbackText={bridgeState.selectedToken}
                    />
                    <span className="font-medium">{bridgeState.selectedToken}</span>
                    <span className="text-muted-foreground text-sm">on {toChainConfig?.name}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Amount Section */}
          <div className="space-y-3">
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Amount</label>
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">
                    Balance: {formatTokenAmount(selectedTokenBalance)} {bridgeState.selectedToken}
                  </div>
                  {selectedTokenBalance !== '0' && selectedTokenPrice > 0 && (
                    <div className="text-xs text-muted-foreground">
                      ≈ {formatUsdValue(getUsdValue(selectedTokenBalance, bridgeState.selectedToken))}
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={loadBalances}
                    className="h-6 px-2 text-xs mt-1"
                  >
                    Refresh
                  </Button>
                </div>
              </div>
              <div className="relative">
                <Input
                  type="number"
                  placeholder="0.00"
                  value={bridgeState.amount}
                  onChange={(e) => updateBridgeState({ amount: e.target.value })}
                  className="bg-secondary/50 border-border/50 pr-16"
                  disabled={bridgeState.isLoading}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 px-2 text-xs text-primary hover:text-primary/80"
                  onClick={setMaxAmount}
                  disabled={bridgeState.isLoading || !selectedTokenBalance}
                >
                  MAX
                </Button>
              </div>
              {bridgeState.amount && (
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>≈ {formatUsdValue(amountUsdValue)}</span>
                  {pricesLoading ? (
                    <span>Loading price...</span>
                  ) : selectedTokenPrice > 0 ? (
                    <span>${selectedTokenPrice.toFixed(4)} per {bridgeState.selectedToken}</span>
                  ) : (
                    <span>Price unavailable</span>
                  )}
                </div>
              )}
            </div>

            {/* Recipient Address */}
            <div className="space-y-1">
              <label className="text-sm font-medium">Recipient Address</label>
              <RecipientAddressInput
                chainId={bridgeState.toChain}
                onAddressChange={(address) => updateBridgeState({ recipientAddress: address })}
              />
            </div>

            {bridgeState.error && (
              <p className="text-xs text-destructive">{bridgeState.error}</p>
            )}
            {!validation.isValid && validation.error && (
              <p className="text-xs text-destructive">{validation.error}</p>
            )}
          </div>

          {/* Fee Estimation */}
          {estimatedFees && bridgeState.amount && parseFloat(bridgeState.amount) > 0 && (
            <div className="p-3 bg-secondary/30 rounded-lg border border-border/50">
              <h3 className="text-sm font-medium mb-2 text-foreground">Estimated Fees</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Bridge Fee:</span>
                  <span className="text-foreground">{estimatedFees.bridgeFee}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network Fee:</span>
                  <span className="text-foreground">{estimatedFees.networkFee}</span>
                </div>
                <div className="flex justify-between font-medium border-t border-border/50 pt-1">
                  <span className="text-foreground">Total Fee:</span>
                  <span className="text-foreground">{estimatedFees.totalFee}</span>
                </div>
              </div>
            </div>
          )}

          {/* Bridge Button */}
          <Button
            onClick={handleBridge}
            disabled={!canBridge}
            className="w-full bg-gradient-primary hover:opacity-90 transition-all duration-300 disabled:opacity-50"
          >
            {bridgeState.isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                Bridge {bridgeState.amount} {bridgeState.selectedToken}
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Security Assessment */}
      <Card className="p-3 bg-gradient-card border-border/50">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-3 w-3 text-primary" />
          <h3 className="font-semibold text-xs">Security Assessment</h3>
        </div>
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Risk Level</span>
            <Badge variant="outline" className="border-success text-success text-xs">Low</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Liquidity</span>
            <Badge variant="outline" className="border-success text-success text-xs">High</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Est. Time</span>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs">~{bridgeState.estimatedTime} min</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Fee Breakdown */}
      <Card className="p-3 bg-gradient-card border-border/50">
        <h3 className="font-semibold text-xs mb-2">Fee Breakdown</h3>
        <div className="space-y-1 text-xs">
          {bridgeState.fees ? (
            <>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Bridge Fee</span>
                <span>{bridgeState.fees.bridgeFee} {bridgeState.selectedToken}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Network Fee</span>
                <span>${bridgeState.fees.networkFee}</span>
              </div>
              <div className="flex justify-between border-t border-border/50 pt-1 font-medium">
                <span>Total Fee</span>
                <span className="text-primary">${bridgeState.fees.totalFee}</span>
              </div>
            </>
          ) : (
            <div className="text-center text-muted-foreground py-2">
              Enter amount to see fees
            </div>
          )}
        </div>
      </Card>



      {/* Bridge Transaction Flow */}
      <BridgeTransactionFlow
        isOpen={showTransactionFlow}
        onClose={() => setShowTransactionFlow(false)}
        transactionData={transactionData}
        onConfirm={handleConfirmBridge}
      />
    </div>
  );
};