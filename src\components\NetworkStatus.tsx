import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChainLogo } from "@/components/TokenLogo";
import { useNetworkStatus } from "@/hooks/useTransactionMonitor";
import { SUPPORTED_CHAINS } from "@/lib/blockchain-config";
import { Wifi, WifiOff, AlertTriangle } from "lucide-react";

export function NetworkStatus() {
  const networkStatus = useNetworkStatus();

  const getStatusIcon = (status: 'online' | 'degraded' | 'offline') => {
    switch (status) {
      case 'online':
        return <Wifi className="h-3 w-3 text-success" />;
      case 'degraded':
        return <AlertTriangle className="h-3 w-3 text-warning" />;
      case 'offline':
        return <WifiOff className="h-3 w-3 text-destructive" />;
    }
  };

  const getStatusColor = (status: 'online' | 'degraded' | 'offline') => {
    switch (status) {
      case 'online':
        return 'border-success text-success';
      case 'degraded':
        return 'border-warning text-warning';
      case 'offline':
        return 'border-destructive text-destructive';
    }
  };

  const getStatusText = (status: 'online' | 'degraded' | 'offline') => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'degraded':
        return 'Degraded';
      case 'offline':
        return 'Offline';
    }
  };

  return (
    <Card className="p-4 bg-gradient-card border-border/50">
      <h3 className="font-semibold text-sm mb-3">Network Status</h3>
      
      <div className="space-y-2">
        {/* Wormhole Network */}
        <div className="flex items-center justify-between">
          <span className="text-sm">Wormhole Network</span>
          <Badge variant="outline" className={`text-xs ${getStatusColor(networkStatus.wormhole)}`}>
            {getStatusIcon(networkStatus.wormhole)}
            <span className="ml-1">{getStatusText(networkStatus.wormhole)}</span>
          </Badge>
        </div>

        {/* Individual Chains */}
        {Object.entries(SUPPORTED_CHAINS).map(([chainId, chain]) => {
          const status = networkStatus[chainId as keyof typeof networkStatus] as 'online' | 'degraded' | 'offline';
          
          return (
            <div key={chainId} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ChainLogo
                  src={chain.logo}
                  alt={chain.name}
                  className="w-4 h-4 rounded-full"
                />
                <span className="text-sm">{chain.name}</span>
              </div>
              <Badge variant="outline" className={`text-xs ${getStatusColor(status)}`}>
                {getStatusIcon(status)}
                <span className="ml-1">{getStatusText(status)}</span>
              </Badge>
            </div>
          );
        })}
      </div>

      <div className="mt-3 pt-3 border-t border-border/50">
        <p className="text-xs text-muted-foreground">
          Last updated: {networkStatus.lastChecked.toLocaleTimeString()}
        </p>
      </div>
    </Card>
  );
}

// Compact version for header
export function NetworkStatusIndicator() {
  const networkStatus = useNetworkStatus();
  
  const overallStatus = (() => {
    const statuses = [
      networkStatus.wormhole,
      networkStatus.solana,
      networkStatus.base,
      networkStatus.arbitrum,
    ];
    
    if (statuses.includes('offline')) return 'offline';
    if (statuses.includes('degraded')) return 'degraded';
    return 'online';
  })();

  const getIndicatorColor = () => {
    switch (overallStatus) {
      case 'online':
        return 'bg-success';
      case 'degraded':
        return 'bg-warning';
      case 'offline':
        return 'bg-destructive';
    }
  };

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${getIndicatorColor()}`} />
      <span className="text-xs text-muted-foreground">
        {overallStatus === 'online' ? 'All systems operational' : 
         overallStatus === 'degraded' ? 'Some issues detected' : 
         'Service disruption'}
      </span>
    </div>
  );
}
